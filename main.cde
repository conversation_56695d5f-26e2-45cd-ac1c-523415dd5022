
	
事件 主窗口.创建完毕()
	初始化()
结束 事件


变量 线程1 为 多线程操作
	
过程 初始化()
	
	保存对象("主窗口",本对象)
	保存对象("系统设置",系统设置1)
	保存对象("JSON操作",JSON操作1)
	保存对象("客户端",客户端0)
	保存对象("BLE蓝牙",BLE蓝牙1)
	
	线程1 = 创建 多线程操作1
	
	初始化硬件配置()
	数据库初始化()
	查询音效结果()
	查询视觉靶校准数据结果()
	查询预瞄渲染结果()
	查询渲染宽度结果()
	查询预染速度结果()
	查询放大偏移X量结果()
	查询放大偏移Y量结果()
	查询弹孔叠加结果()
	查询自动结束结果()
	查询射击音效结果()
	查询上弹音效结果()
	查询靶面编号结果()
	初始化UI全局()
	初始游戏软参数()
	查询管理员结果过程()
	刷新UI全局()
结束 过程



过程 初始化BLE蓝牙()
	BLE蓝牙1.初始化()
	BLE蓝牙1.置可被发现()
	延时(3000)
	位置传感器1.开始监测()
	BLE蓝牙1.开始搜索()
	BLE1时钟.时钟周期 = 20 * 1000
	
结束 过程

变量 BLE蓝牙1确认 为 逻辑型
变量 BLE蓝牙2确认 为 逻辑型
变量 BLE蓝牙步骤 为 整数型
变量 确认的蓝牙设备 为 文本型(3,3)
变量 重量1 为 文本型
变量 重量2 为 文本型
事件 BLE蓝牙1.发现设备(名称 为 文本型,地址 为 文本型,MajorID 为 整数型,MinorID 为 整数型,配对状态 为 整数型)
	如果 名称 = "JDY-33-BLE1" 则
		确认的蓝牙设备(0,1) = 名称
		确认的蓝牙设备(0,2) = 地址
		BLE蓝牙1确认 = 真
	结束 如果
	
	如果 名称 = "JDY-33-BLE2" 则
		确认的蓝牙设备(1,1) = 名称
		确认的蓝牙设备(1,2) = 地址
		BLE蓝牙2确认 = 真
		BLE连接时钟.时钟周期 = 1000
	结束 如果
结束 事件

事件 BLE连接时钟.周期事件()
	如果 BLE蓝牙1确认 = 真 且 BLE蓝牙步骤 = 0 则
		BLE蓝牙1.连接设备(确认的蓝牙设备(0,2))
	结束 如果
	
	如果 BLE蓝牙2确认 = 真 且 BLE蓝牙步骤 = 2 则
		BLE蓝牙1.连接设备(确认的蓝牙设备(1,2))
	结束 如果
结束 事件

变量 BLE蓝牙连接状态 为 逻辑型	
事件 BLE蓝牙1.连接状态改变(状态 为 整数型)
	如果 状态 = 1 则
		'弹出提示("已连接")		
	否则	
		弹出提示("已断开")
	结束 如果
结束 事件

事件 BLE蓝牙1.发现服务(服务信息 为 集合)
	如果 服务信息.取项目总数() < 0 则
		退出
	结束 如果

	变量 计次 为 整数型
	变量 信息数组 为 文本型()
	变量 分组索引 为 整数型
	变量 计次2 为 整数型
	'变量 通道属性 为 文本型
	'弹出提示("发现服务")
	'分组列表框1.清空所有数据()	
	计次 = 0
	判断循环首 计次 < 服务信息.取项目总数()		
		信息数组 = 服务信息.取项目(计次)
		'分组索引 = 分组列表框1.添加分组("服务" & (计次 + 1),信息数组(0)) '信息数组中的第一个成员为服务,剩下的其他成员都是通道		
		计次2 = 0
		判断循环首 计次2 < 取数组成员数(信息数组)
			如果 计次2 > 0 则
				'通道属性 = 取通道属性(信息数组(0),信息数组(计次2))
				'分组列表框1.添加子项(分组索引,"logo.png","通道" & 计次2 & "  " & 通道属性,信息数组(计次2),"","")
				如果 BLE蓝牙步骤 = 0 则
					'延时(100)
					'BLE蓝牙1.写入数据("0000ffe0-0000-1000-8000-00805f9b34fb","0000ffe1-0000-1000-8000-00805f9b34fb",十六进制到字节集("AA00A9ABA8"))'零点校准
					延时(100)
					BLE蓝牙1.写入数据("0000ffe0-0000-1000-8000-00805f9b34fb","0000ffe1-0000-1000-8000-00805f9b34fb",十六进制到字节集("ABOOAAACAD"))'去皮
					延时(100)
					BLE蓝牙1.写入数据("0000ffe0-0000-1000-8000-00805f9b34fb","0000ffe1-0000-1000-8000-00805f9b34fb",十六进制到字节集("A400A3A5A2"))'实时测重
					延时(100)
					BLE蓝牙1.读取数据("0000ffe0-0000-1000-8000-00805f9b34fb","0000ffe1-0000-1000-8000-00805f9b34fb")'读取
	
				结束 如果
				
				如果 BLE蓝牙步骤 = 2 则
					'延时(100)
					'BLE蓝牙1.写入数据("0000fff0-0000-1000-8000-00805f9b34fb","0000fff1-0000-1000-8000-00805f9b34fb",十六进制到字节集("AA00A9ABA8"))'零点校准
					延时(100)
					BLE蓝牙1.写入数据("0000fff0-0000-1000-8000-00805f9b34fb","0000fff1-0000-1000-8000-00805f9b34fb",十六进制到字节集("ABOOAAACAD"))'去皮
					延时(100)
					BLE蓝牙1.写入数据("0000fff0-0000-1000-8000-00805f9b34fb","0000fff1-0000-1000-8000-00805f9b34fb",十六进制到字节集("A400A3A5A2"))'实时测重
					延时(100)
					BLE蓝牙1.读取数据("0000fff0-0000-1000-8000-00805f9b34fb","0000fff1-0000-1000-8000-00805f9b34fb")'读取
				结束 如果
			结束 如果
			计次2 = 计次2 + 1
		判断循环尾
		计次 = 计次 + 1
	判断循环尾
结束 事件


事件 BLE蓝牙1.读取数据完毕(结果 为 整数型,服务UUID 为 文本型,通道UUID 为 文本型,数据 为 字节型()) 
	如果 结果 = 1 则
		弹出提示("读取数据成功：" & 通道UUID & "\n数据：" & 字节集到十六进制(数据))
	结束 如果	
结束 事件

函数 十六进制字符转整数(十六进制字符 为 文本型) 为 整数型
	变量 字符1 为 文本型
	变量 字符2 为 文本型
	变量 值1 为 整数型
	变量 值2 为 整数型

	'获取两个字符
	字符1 = 取文本左边(十六进制字符, 1)
	字符2 = 取文本右边(十六进制字符, 1)

	'转换第一个字符
	判断 字符1
		分支 "0"
			值1 = 0
		分支 "1"
			值1 = 1
		分支 "2"
			值1 = 2
		分支 "3"
			值1 = 3
		分支 "4"
			值1 = 4
		分支 "5"
			值1 = 5
		分支 "6"
			值1 = 6
		分支 "7"
			值1 = 7
		分支 "8"
			值1 = 8
		分支 "9"
			值1 = 9
		分支 "A"
			值1 = 10
		分支 "B"
			值1 = 11
		分支 "C"
			值1 = 12
		分支 "D"
			值1 = 13
		分支 "E"
			值1 = 14
		分支 "F"
			值1 = 15
		分支 否则
			值1 = 0
	结束 判断

	'转换第二个字符
	判断 字符2
		分支 "0"
			值2 = 0
		分支 "1"
			值2 = 1
		分支 "2"
			值2 = 2
		分支 "3"
			值2 = 3
		分支 "4"
			值2 = 4
		分支 "5"
			值2 = 5
		分支 "6"
			值2 = 6
		分支 "7"
			值2 = 7
		分支 "8"
			值2 = 8
		分支 "9"
			值2 = 9
		分支 "A"
			值2 = 10
		分支 "B"
			值2 = 11
		分支 "C"
			值2 = 12
		分支 "D"
			值2 = 13
		分支 "E"
			值2 = 14
		分支 "F"
			值2 = 15
		分支 否则
			值2 = 0
	结束 判断

	'计算最终值：第一个字符×16 + 第二个字符
	十六进制字符转整数 = 值1 * 16 + 值2
结束 函数

函数 数据转克重(数据 为 字节型()) 为 文本型
	变量 转换数据 为 文本型
	变量 十六进制数据 为 文本型
	变量 数据长度 为 整数型
	变量 十六进制字符 为 文本型
	变量 数据1 为 整数型
	变量 数据2 为 整数型
	变量 数据3 为 整数型
	变量 重量值 为 整数型

	'将字节数组转换为16进制字符串
	十六进制数据 = 字节集到十六进制(数据)

	'验证数据长度，应该是20个字符（10字节的16进制表示）
	数据长度 = 取文本长度(十六进制数据)
	如果 数据长度 <> 20 则
		数据转克重 = "长度错误"
		退出
	结束 如果

	'验证数据格式，前4个字符应该是AA
	如果 取文本左边(十六进制数据, 2) <> "AA" 则
		数据转克重 = "格式错误"
		退出
	结束 如果

	'提取第4、5、6字节的16进制值（第7-12个字符）
	'第4字节：第7-8个字符
	十六进制字符 = 取文本中间(十六进制数据, 7, 2)
	数据1 = 十六进制字符转整数(十六进制字符)

	'第5字节：第9-10个字符
	十六进制字符 = 取文本中间(十六进制数据, 9, 2)
	数据2 = 十六进制字符转整数(十六进制字符)

	'第6字节：第11-12个字符
	十六进制字符 = 取文本中间(十六进制数据, 11, 2)
	数据3 = 十六进制字符转整数(十六进制字符)

	'按照24位有符号整数计算：数据1×65536 + 数据2×256 + 数据3
	重量值 = 数据1 * 65536 + 数据2 * 256 + 数据3

	'处理24位有符号数的负数情况（补码转换）
	如果 重量值 >= 8388608 则  '2^23 = 8388608
		重量值 = 重量值 - 16777216  '2^24 = 16777216
	结束 如果

	'校正重量值：根据测试结果，需要乘以校正系数
	'100g砝码显示6g，200g砝码显示12g，校正系数约为16.67
	重量值 = 重量值 * 50 / 3  '50/3 ≈ 16.67

	'确保结果为整数
	重量值 = 到整数(重量值)

	'转换为文本并返回
	转换数据 = 到文本(重量值) & "克"
	数据转克重 = 转换数据
结束 函数

事件 BLE蓝牙1.通道数据改变(服务UUID 为 文本型,通道UUID 为 文本型,数据 为 字节型()) 
	'弹出提示("通道数据改变" & 通道UUID & "\n数据：" & 字节集到十六进制(数据))
	
	如果 通道UUID = "0000ffe1-0000-1000-8000-00805f9b34fb" 则
		如果 BLE蓝牙步骤 = 0 则
			重量1 =  "0.0克"
			BLE蓝牙步骤 = 2
		结束 如果
		
		重量1 =  数据转克重(数据)
		标签2.标题 = 重量1
	结束 如果
	
	如果 通道UUID = "0000fff1-0000-1000-8000-00805f9b34fb" 则
		如果 BLE蓝牙步骤 = 2 则
			重量2 =  "0.0克"
			BLE蓝牙步骤 = 3
		结束 如果
		
		重量2 = 数据转克重(数据)
		标签3.标题 = 重量2
	结束 如果
	
结束 事件

事件 BLE蓝牙1.写入数据完毕(结果 为 整数型) 
	如果 结果 = 1 则
		'弹出提示("BLE蓝牙1写入数据成功")
	否则	
		弹出提示("BLE蓝牙1写入数据失败")
	结束 如果
结束 事件

	变量 客户端001地址 为 文本型
	变量 客户端001端口 为 整数型
	变量 客户端连接成功 为 逻辑型
	变量 WIFI客户端成功 为 逻辑型
	变量 丢包状态计数 为 整数型
	变量 初始屏幕亮度 为 整数型
	变量 屏幕亮度 为 整数型
	变量 屏幕分辨率 为 文本型

过程 初始化硬件配置()
    系统设置1.保持屏幕常亮()   '禁止系统对屏幕控制,保持屏幕常亮
	系统设置1.置屏幕亮度模式(1)'亮度手动调节
	初始屏幕亮度 = 255
	系统设置1.置屏幕亮度(初始屏幕亮度)
	屏幕亮度 = 系统设置1.取屏幕亮度()
	数据报1.开始监听(8612)
	数据报1.置接收数据包大小(4096)
	服务器1.启动服务器(8611)
	服务器1.置接收数据包大小(4096)
	客户端连接成功 = 假
	WIFI客户端成功 = 假
	终端检测时钟.时钟周期 = 5000
	屏幕分辨率 = 取屏幕宽度() & "X" & 取屏幕高度()
	初始化BLE蓝牙()
结束 过程
事件 客户端0.创建完毕()
	客户端0.连接服务器("127.0.0.1","8686",5000)
结束 事件
事件 客户端0.连接完毕(连接结果 为 逻辑型)
	如果 连接结果 = 真 则
		公用模块.HOME程序悬浮按钮可视过程(真)
	结束 如果
结束 事件


变量 配置参数 为 文本型
变量 账户数据 为 文本型
变量 射击数据 为 文本型
	
过程 数据库初始化()
	
	配置参数 = "config"
	账户数据 = "userAccount"
	射击数据 = "shootingLog"
	
	如果 文件是否存在(取存储卡路径() & "/HDDZ/") = 假 则
		创建目录(取存储卡路径() & "/HDDZ/")
	结束 如果
	
	如果 文件是否存在(取存储卡路径() & "/HDDZ/shootingTraining/") = 假 则
		创建目录(取存储卡路径() & "/HDDZ/shootingTraining/")
	结束 如果
	
	如果 文件是否存在(取存储卡路径() & "/HDDZ/shootingTraining/data.db") = 假 则
		创建数据库(取存储卡路径() & "/HDDZ/shootingTraining/data.db")
	结束 如果
	
	打开数据库(取存储卡路径() & "/HDDZ/shootingTraining/data.db") '！！！在进行数据库操作前必须先打开数据库
	
	如果 数据表是否存在(账户数据) = 假 则
		创建数据表(账户数据,"id INTEGER PRIMARY KEY, name text, phone text, password text, shootingCount text, accuracy text, hitTotal text")
	结束 如果
	
	如果 数据表是否存在(射击数据) = 假 则
		创建数据表(射击数据,"id INTEGER PRIMARY KEY, belong text, ring text, direction text, interval text, accuracy text, hitTotal text, preview text")
	结束 如果
	
	如果 数据表是否存在(配置参数) = 假 则
		'数据库执行("CREATE TABLE " & 数据库表名 & " (id text,value text)") '创建数据表,表的结构有两个列,第一列为id,第二列为value
		'如果要创建自增型id字段,可以这样写 创建数据表(数据库表名,"id integer PRIMARY KEY,value text")
		'voice                  ：音效选择
		创建数据表(配置参数,"type text,value text")
		判断 屏幕分辨率
			分支 "1280X800"
				数据库执行("INSERT INTO " & 配置参数 & " VALUES ('panel',600)") 
				数据库执行("INSERT INTO " & 配置参数 & " VALUES ('coreX',300)") 
				数据库执行("INSERT INTO " & 配置参数 & " VALUES ('coreY',362)") 
				数据库执行("INSERT INTO " & 配置参数 & " VALUES ('upEdgeY',287)") 
				数据库执行("INSERT INTO " & 配置参数 & " VALUES ('dowEdgeY',-286)") 
				数据库执行("INSERT INTO " & 配置参数 & " VALUES ('leftEdgeX',-286)") 
				数据库执行("INSERT INTO " & 配置参数 & " VALUES ('rightEdgeX',287)")
				数据库执行("INSERT INTO " & 配置参数 & " VALUES ('bulletOffsetX',-3)") 
				数据库执行("INSERT INTO " & 配置参数 & " VALUES ('bulletOffsetY',22)")
			分支 "2160X1080"
				数据库执行("INSERT INTO " & 配置参数 & " VALUES ('panel',868)") 
				数据库执行("INSERT INTO " & 配置参数 & " VALUES ('coreX',432)") 
				数据库执行("INSERT INTO " & 配置参数 & " VALUES ('coreY',623)") 
				数据库执行("INSERT INTO " & 配置参数 & " VALUES ('upEdgeY',499)") 
				数据库执行("INSERT INTO " & 配置参数 & " VALUES ('dowEdgeY',-324)") 
				数据库执行("INSERT INTO " & 配置参数 & " VALUES ('leftEdgeX',-411)") 
				数据库执行("INSERT INTO " & 配置参数 & " VALUES ('rightEdgeX',414 )")
				数据库执行("INSERT INTO " & 配置参数 & " VALUES ('bulletOffsetX',-1)") 
				数据库执行("INSERT INTO " & 配置参数 & " VALUES ('bulletOffsetY',0)")
			分支 否则
				数据库执行("INSERT INTO " & 配置参数 & " VALUES ('panel',200)") 
				数据库执行("INSERT INTO " & 配置参数 & " VALUES ('coreX',24)") 
				数据库执行("INSERT INTO " & 配置参数 & " VALUES ('coreY',67)") 
				数据库执行("INSERT INTO " & 配置参数 & " VALUES ('upEdgeY',311)") 
				数据库执行("INSERT INTO " & 配置参数 & " VALUES ('dowEdgeY',-304)") 
				数据库执行("INSERT INTO " & 配置参数 & " VALUES ('leftEdgeX',-294)") 
				数据库执行("INSERT INTO " & 配置参数 & " VALUES ('rightEdgeX',325)")
				数据库执行("INSERT INTO " & 配置参数 & " VALUES ('bulletOffsetX',0)") 
				数据库执行("INSERT INTO " & 配置参数 & " VALUES ('bulletOffsetY',0)") 
		结束 判断
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('voice','女')")'语音音效结果
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('bulletStack','开')")'弹孔叠加结果
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('autoend','开')")'自动结束结果
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('autoEndValue','20')")'自动结束值
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('shootingSound','开')")'射击音效开关选择结果
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('shootingSoundValue','1')")'射击音效选择结果
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('topPlaySound','开')")'上弹音效开关选择结果
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('topPlaySoundValue','1')")'上弹音效选择结果
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('target','1')")'靶面选择结果
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('previewRender','开')")'预瞄渲染
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('renderWidth','5')")'渲染宽度
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('renderSpeed','5')")'渲染速度
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('calibration','eyJ4IjoxNjMsICJ5IjoxMjMsICJyIjozMCwgIm1hZ25pdHVkZSI6Nzg2Nn0=')")'视觉靶校准数据
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('ZoomOffsetX','98')")'放大偏移X
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('ZoomOffsetY','117')")'放大偏移Y
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('manage','{\"account\":\"admin\"&\"password\":\"123456\"}')")'管理员
		
	结束 如果
	
	'考虑升级APP的触发条件
	
	变量 放大偏移X 为 文本型
	放大偏移X = 查询数据库("config", "ZoomOffsetX")
	如果 放大偏移X = "" 则
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('ZoomOffsetX','98')")'放大偏移X
	结束 如果
	
	变量 放大偏移Y 为 文本型
	放大偏移Y = 查询数据库("config", "ZoomOffsetY")
	如果 放大偏移Y = "" 则
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('ZoomOffsetY','117')")'放大偏移Y
	结束 如果

	变量 视觉校准数据 为 文本型
	视觉校准数据 = 查询数据库("config", "calibration")
	如果 视觉校准数据 = "" 则
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('calibration','eyJ4IjoxNjMsICJ5IjoxMjMsICJyIjozMCwgIm1hZ25pdHVkZSI6Nzg2Nn0')")'视觉靶校准数据
	结束 如果
	
	变量 渲染速度值 为 文本型
	渲染速度值 = 查询数据库("config", "renderSpeed")
	如果 渲染速度值 = "" 则
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('renderSpeed','5')")
	结束 如果
	
	变量 预瞄渲染状态 为 文本型
	预瞄渲染状态 = 查询数据库("config", "previewRender")
	如果 预瞄渲染状态 = "" 则
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('previewRender','关')")
	结束 如果	

	变量 查询射击音效结果 为 文本型
	查询射击音效结果 = 查询数据库("config", "shootingSound")
	如果 查询射击音效结果 = "" 则
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('shootingSound','开')")
	结束 如果
	
	变量 查询射击音效 为 文本型
	查询射击音效 = 查询数据库("config", "shootingSoundValue")
	如果 查询射击音效 = "" 则
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('shootingSoundValue','1')")
	结束 如果
	
	变量 查询上弹结果 为 文本型
	查询上弹结果 = 查询数据库("config", "topPlaySound")
	如果 查询上弹结果 = "" 则
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('topPlaySound','开')")
	结束 如果
	
	变量 查询上弹音效值 为 文本型
	查询上弹音效值 = 查询数据库("config", "topPlaySoundValue")
	如果 查询上弹音效值 = "" 则
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('topPlaySoundValue','1')")
	结束 如果
	
	变量 查询自动结束值 为 文本型
	查询自动结束值 = 查询数据库("config", "autoEndValue")
	如果 查询自动结束值 = "" 则
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('autoEndValue','20')")
	结束 如果
		
	变量 射击音效开关值 为 文本型
	射击音效开关值 = 查询数据库("config", "shootingSound")
	如果 射击音效开关值 = "" 则
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('shootingSound','开')")'射击音效开关选择结果
	结束 如果
	
	变量 射击音效选择值 为 文本型
	射击音效选择值 = 查询数据库("config", "shootingSoundValue")
	如果 射击音效选择值 = "" 则
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('shootingSoundValue','1')")'射击音效选择结果
	结束 如果
	
	变量 上弹音效开关值 为 文本型
	上弹音效开关值 = 查询数据库("config", "topPlaySound")
	如果 上弹音效开关值 = "" 则
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('topPlaySound','开')")'上弹音效开关选择结果
	结束 如果
	
	变量 上弹音效选择值 为 文本型
	上弹音效选择值 = 查询数据库("config", "topPlaySoundValue")
	如果 上弹音效选择值 = "" 则
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('topPlaySoundValue','1')")'上弹音效选择结果
	结束 如果
	
	变量 靶面选择值 为 文本型
	靶面选择值 = 查询数据库("config", "target")
	如果 靶面选择值 = "" 则
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('target','1')")'靶面选择结果
	结束 如果
	
	变量 预瞄渲染开关值 为 文本型
	预瞄渲染开关值 = 查询数据库("config", "previewRender")
	如果 预瞄渲染开关值 = "" 则
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('previewRender','开')")'预瞄渲染
	结束 如果
	
	变量 渲染宽度值 为 文本型
	渲染宽度值 = 查询数据库("config", "renderWidth")
	如果 渲染宽度值 = "" 则
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('renderWidth','5')")'渲染宽度
	结束 如果
	
	变量 管理员 为 文本型
	管理员 = 查询数据库("config", "manage")
	如果 管理员 = "" 则
		数据库执行("INSERT INTO " & 配置参数 & " VALUES ('manage','{\"account\":\"admin\"&\"password\":\"123456\"}')")'管理员
	结束 如果

结束 过程

函数 查询数据库(表 为 文本型, 字段 为 文本型) 为 文本型
	变量 查询结果 为 文本型 
	变量 整理结果 为 文本型 
	查询结果 = 数据库查询("SELECT * FROM " & 表 & " WHERE type='" & 字段 & "'","","") & ","
	整理结果  = 取指定文本2(查询结果,字段,",")
	'返回结果值
    查询数据库 = 整理结果
结束 函数
	
函数 修改数据库(表 为 文本型, 字段 为 文本型, 键 为 文本型, 值 为 文本型) 为 文本型
	数据库执行("UPDATE " & 表 & " SET "& 键 &"='"& 值 &"' WHERE type='"& 字段 &"'")
	变量 查询结果 为 文本型 
	变量 整理结果 为 文本型 
	查询结果 = 数据库查询("SELECT * FROM " & 表 & " WHERE type='" & 字段 & "'","","") & ","
	整理结果  = 取指定文本2(查询结果,字段,",")
	'返回结果值
	修改数据库 = 整理结果
结束 函数

过程 查询放大偏移X量结果()
	公用模块.放大偏移X量 = 查询数据库("config", "ZoomOffsetX")
结束 过程

过程 查询放大偏移Y量结果()
	公用模块.放大偏移Y量 = 查询数据库("config", "ZoomOffsetY")
结束 过程

变量 视觉靶校准数据 为 文本型
过程 查询视觉靶校准数据结果()
	视觉靶校准数据 = 查询数据库("config", "calibration")
	视觉靶校准数据 = 字节到文本(BASE64解码(视觉靶校准数据),"UTF-8")
结束 过程

过程 查询预染速度结果()
	公用模块.渲染速度 = 查询数据库("config", "renderSpeed")
	回放速度调整标签.标题 = 公用模块.渲染速度
结束 过程

过程 查询预瞄渲染结果()
	公用模块.预瞄渲染结果 = 查询数据库("config", "previewRender")
结束 过程

过程 查询渲染宽度结果()
	公用模块.渲染宽度 = 查询数据库("config", "renderWidth")
结束 过程

过程 恢复屏幕亮度()
	高级对话框1.关闭对话框()
	屏幕亮度 = 初始屏幕亮度
	屏保计时 = 0
结束 过程

变量 语音结果 为 文本型
过程 查询音效结果()
	语音结果 = 查询数据库("config", "voice")
结束 过程

	变量 弹孔叠加结果 为 文本型
过程 查询弹孔叠加结果()
	弹孔叠加结果 = 查询数据库("config", "bulletStack")
结束 过程

	变量 自动结束结果 为 文本型
	变量 自动结束值结果 为 文本型
过程 查询自动结束结果()
	自动结束结果 = 查询数据库("config", "autoend")
	自动结束值结果 = 查询数据库("config", "autoEndValue")
结束 过程

    变量 射击音效结果 为 文本型
	变量 射击音效值结果 为 文本型
过程 查询射击音效结果()
	射击音效结果 = 查询数据库("config", "shootingSound")
	射击音效值结果 = 查询数据库("config", "shootingSoundValue")
结束 过程

    变量 上弹音效结果 为 文本型
	变量 上弹音效值结果 为 文本型
过程 查询上弹音效结果()
	上弹音效结果 = 查询数据库("config", "topPlaySound")
	上弹音效值结果 = 查询数据库("config", "topPlaySoundValue")
结束 过程

    变量 靶面编号 为 文本型
过程 查询靶面编号结果()
	靶面编号 = 查询数据库("config", "target")
	渲染面板()
结束 过程

    变量 管理员 为 文本型
过程 查询管理员结果过程()
	管理员 = 查询数据库("config", "manage")
	管理员 = 子文本替换(管理员,"&",",")
	'弹出提示(管理员)
结束 过程

变量 设置开关 为 整数型
变量 游戏开始状态 为 逻辑型
变量 射击序列 为 整数型

变量 计时周期 为 双精度小数型
变量 命中环数 为 双精度小数型
变量 播放音效 为 文本型
变量 命中方向 为 文本型
变量 射击精度最大百分比 为 双精度小数型
变量 射击精度损耗统计 为 双精度小数型
变量 射击精度统计 为 双精度小数型
变量 射击时间 为 文本型
变量 命中总合 为 双精度小数型
变量 模拟射击过程 为 整数型

过程 初始游戏软参数()
	公用模块.回放计次 = 0
	模拟射击过程 = 0
	屏幕亮度 = 170
	开始计时 = 0
    计时周期 = 0
	回放时钟.时钟周期 = 0
	公用模块.开始的结束 = 假
	公用模块.射击计数 = 0
	公用模块.射击计数统计 = 1
	公用模块.列表序号 = 1
	公用模块.英文靶环方向 = ""
    公用模块.靶环环数 = ""
	公用模块.报环类型 = -1
	公用模块.播报流程 = 0
	设置开关 = 0
	射击序列 = 0
	命中环数 = 0.00
	命中方向 = "无"
	射击精度最大百分比 = 100
	射击精度损耗统计 = 0
	射击精度统计 = 100
	射击时间 = "2022-01-11 11:00"
	命中总合 = 0.00
	游戏开始状态 = 假
	统计列表.清空项目()
	清理弹孔()
	公用模块.校准状态 = 假
	公用模块.快速时间设置 = 0
结束 过程

变量 本机X最大值 为 整数型
变量 本机X最小值 为 整数型
变量 本机Y最大值 为 整数型
变量 本机Y最小值 为 整数型
变量 UI调整状态 为 整数型'=1调整高级表格
变量 统计表格左边 为 整数型
变量 统计表格顶边 为 整数型
变量 统计表格宽 为 整数型
变量 统计表格高 为 整数型
变量 高级列表左边 为 整数型
变量 高级列表顶边 为 整数型
变量 高级列表宽 为 整数型
变量 高级列表高 为 整数型
变量 面板参数 为 文本型
变量 面板宽度 为 整数型
变量 面板高度 为 整数型
变量 靶环中心点X 为 整数型
变量 靶环中心点Y 为 整数型
变量 校准准星 为 图片框
变量 弹孔0 为 图片框
变量 弹孔1 为 图片框
变量 弹孔2 为 图片框
变量 弹孔3 为 图片框
变量 弹孔4 为 图片框
变量 弹孔5 为 图片框
变量 弹孔6 为 图片框
变量 弹孔7 为 图片框
变量 弹孔8 为 图片框
变量 弹孔9 为 图片框
变量 弹孔10 为 图片框
变量 弹孔11 为 图片框
变量 弹孔12 为 图片框
变量 弹孔13 为 图片框
变量 弹孔14 为 图片框
变量 弹孔15 为 图片框
变量 弹孔16 为 图片框
变量 弹孔17 为 图片框
变量 弹孔18 为 图片框
变量 弹孔19 为 图片框
变量 弹孔20 为 图片框

过程 处理预瞄UI()
	
	预瞄轨迹标签.背景颜色 = &HFF292E32
	预瞄轨迹画板.背景颜色 = &HFF292E32
	压力曲线标签.背景颜色 = &HFF292E32
    回放速度标签.背景颜色 = &HFF292E32
		
	如果 公用模块.预瞄渲染结果 = "关" 或 公用模块.预瞄渲染结果 = "开" 且 设置开关 = 1 则
		压力曲线标签.可视 = 假
		压力折线图.可视 = 假
		回放上一个.可视 = 假
		回放标记标签.可视 = 假
		回放下一个按钮.可视 = 假
		回放速度标签.可视 = 假
		回放减速按钮.可视 = 假
		回放速度调整标签.可视 = 假
		回放加速按钮.可视 = 假
		模拟射击按钮.可视 = 真
		模拟射击按钮.到顶层()
		模拟射击按钮.左边 = 672
		模拟射击按钮.顶边 = 620
		模拟射击按钮.宽度 = 292
		模拟射击按钮.高度 = 36
		
		预瞄轨迹放大设置背景.背景颜色 = &HFF292E32
		预瞄轨迹放大设置背景.可视 = 真
		预瞄轨迹放大设置背景.左边 = 843
		预瞄轨迹放大设置背景.顶边 = 418
		预瞄轨迹放大设置背景.宽度 = 117
		预瞄轨迹放大设置背景.高度 = 198
		
		预瞄轨迹标签.可视 = 真
		预瞄轨迹标签.左边 = 675
		预瞄轨迹标签.顶边 = 418
	    预瞄轨迹画板.可视 = 真
		预瞄轨迹画板.左边 = 675
		预瞄轨迹画板.顶边 = 460
	    预瞄轨迹准星.可视 = 真
		预瞄轨迹准星.左边 = 714
		预瞄轨迹准星.顶边 = 497
		
		缩放校准标签.可视 = 真
		缩放校准标签.左边 = 852
		缩放校准标签.顶边 = 425
		缩放校准值标签.可视 = 真
		缩放校准值标签.标题 = "X:" & 公用模块.放大偏移X量 & "," & "Y:" & 公用模块.放大偏移Y量
		缩放校准值标签.左边 = 852
		缩放校准值标签.顶边 = 448

		缩放校准X减按钮.可视 = 真
		缩放校准X减按钮.左边 = 844
		缩放校准X减按钮.顶边 = 490
		缩放校准X减按钮.宽度 = 54
		缩放校准X减按钮.高度 = 44
		
		缩放校准X加按钮.可视 = 真
		缩放校准X加按钮.左边 = 902
		缩放校准X加按钮.顶边 = 490
		缩放校准X加按钮.宽度 = 54
		缩放校准X加按钮.高度 = 44
		
		缩放校准Y减按钮.可视 = 真
		缩放校准Y减按钮.左边 = 844
		缩放校准Y减按钮.顶边 = 545
		缩放校准Y减按钮.宽度 = 54
		缩放校准Y减按钮.高度 = 44
		
		缩放校准Y加按钮.可视 = 真
		缩放校准Y加按钮.左边 = 902
		缩放校准Y加按钮.顶边 = 545
		缩放校准Y加按钮.宽度 = 54
		缩放校准Y加按钮.高度 = 44
		关闭历史6枪()
	结束 如果
	
	如果 公用模块.预瞄渲染结果 = "开" 且 设置开关 = 0 且 靶面编号 = "1" 则
	    压力折线图.初始化图表("","持续时间","压力(克)",6,&HFF292E32,绿色)
	    预瞄轨迹标签.可视 = 真
		预瞄轨迹标签.左边 = 689
		预瞄轨迹标签.顶边 = 432
	    预瞄轨迹画板.可视 = 真
		预瞄轨迹画板.左边 = 688
		预瞄轨迹画板.顶边 = 474
	    预瞄轨迹准星.可视 = 真
		预瞄轨迹准星.左边 = 728
		预瞄轨迹准星.顶边 = 511
		模拟射击按钮.可视 = 假
	    压力曲线标签.可视 = 真
	    压力折线图.可视 = 真
	    回放上一个.可视 = 真
	    回放标记标签.可视 = 真
	    回放下一个按钮.可视 = 真
		回放速度标签.可视 = 真
		回放减速按钮.可视 = 真
		回放速度调整标签.可视 = 真
		回放加速按钮.可视 = 真
		预瞄轨迹放大设置背景.可视 = 假
		缩放校准标签.可视 = 假
		缩放校准值标签.可视 = 假
		缩放校准X减按钮.可视 = 假
		缩放校准X加按钮.可视 = 假
		缩放校准Y减按钮.可视 = 假
		缩放校准Y加按钮.可视 = 假
		关闭历史6枪()
	结束 如果
	
	如果 公用模块.预瞄渲染结果 = "关" 且 设置开关 = 0 则
		预瞄轨迹标签.可视 = 假
		预瞄轨迹画板.可视 = 假
		预瞄轨迹准星.可视 = 假
		压力曲线标签.可视 = 假
		压力折线图.可视 = 假
		回放上一个.可视 = 假
		回放标记标签.可视 = 假
		回放下一个按钮.可视 = 假
		回放速度标签.可视 = 假
		回放减速按钮.可视 = 假
		回放速度调整标签.可视 = 假
		回放加速按钮.可视 = 假
		模拟射击按钮.可视 = 假
		缩放校准标签.可视 = 假
		缩放校准值标签.可视 = 假
		缩放校准X减按钮.可视 = 假
		缩放校准X加按钮.可视 = 假
		缩放校准Y加按钮.可视 = 假
		缩放校准Y减按钮.可视 = 假
		预瞄轨迹放大设置背景.可视 = 假
		开启历史6枪()
	结束 如果
	
结束 过程

事件 画板1.创建完毕()
	保存对象("画板1",画板1)
	画板1.可停留焦点 = 假
结束 事件

事件 预瞄轨迹画板.创建完毕()
	保存对象("预瞄轨迹画板",预瞄轨迹画板)
结束 事件

事件 压力折线图.创建完毕()
	压力折线图.左边 = 864
	压力折线图.顶边 = 474
	压力折线图.宽度 = 394
	压力折线图.高度 = 150
	压力折线图.初始化图表("","持续时间","压力(克)",6,&HFF292E32,绿色)
	保存对象("压力折线图",压力折线图)
结束 事件

事件 回放时钟.创建完毕()
	保存对象("回放时钟",回放时钟)
结束 事件


'2022-02-09,开始开发快速计时和历史6枪,最新6枪记录
'UI部分的对象变量:
变量 历史六枪背景 为 图片框
变量 历史一枪记录 为 标签
变量 历史二枪记录 为 标签
变量 历史三枪记录 为 标签
变量 历史四枪记录 为 标签
变量 历史五枪记录 为 标签
变量 历史六枪记录 为 标签
变量 历史六枪统计 为 标签
变量 历史六枪计数 为 整数型
变量 历史六枪数组 为 双精度小数型(6)

变量 历史靶面1 为 画板
变量 历史靶面2 为 画板
变量 历史靶面3 为 画板
变量 历史靶面4 为 画板
变量 历史靶面5 为 画板

变量 射击总时背景 为 标签
变量 射击总时标题 为 标签
变量 射击总时时间 为 标签

变量 快速时间设置背景 为 标签
变量 快速时间设置下拉框 为 下拉列表框
变量 快速时间设置1 为 按钮
变量 快速时间设置2 为 按钮
变量 快速时间设置3 为 按钮

变量 学员欢迎标签 为 标签

过程 关闭历史6枪()
	历史六枪背景.可视 = 假
	历史一枪记录.可视 = 假
	历史二枪记录.可视 = 假
	历史三枪记录.可视 = 假
	历史四枪记录.可视 = 假
	历史五枪记录.可视 = 假
	历史六枪记录.可视 = 假
	历史六枪统计.可视 = 假
	历史靶面1.可视 = 假
	历史靶面2.可视 = 假
	历史靶面3.可视 = 假
	历史靶面4.可视 = 假
	历史靶面5.可视 = 假
	射击总时背景.可视 = 假
	射击总时标题.可视 = 假
	射击总时时间.可视 = 假
	快速时间设置背景.可视 = 假
	快速时间设置下拉框.可视 = 假
 	快速时间设置1.可视 = 假
	快速时间设置2.可视 = 假
	快速时间设置3.可视 = 假
结束 过程

过程 开启历史6枪()
	历史六枪背景.可视 = 真
	历史一枪记录.可视 = 真
	历史二枪记录.可视 = 真
	历史三枪记录.可视 = 真
	历史四枪记录.可视 = 真
	历史五枪记录.可视 = 真
	历史六枪记录.可视 = 真
	历史六枪统计.可视 = 真
	历史靶面1.可视 = 真
	历史靶面2.可视 = 真
	历史靶面3.可视 = 真
	历史靶面4.可视 = 真
	历史靶面5.可视 = 真
	射击总时背景.可视 = 真
	射击总时标题.可视 = 真
	射击总时时间.可视 = 真
	快速时间设置背景.可视 = 真
	快速时间设置下拉框.可视 = 真
 	快速时间设置1.可视 = 真
	快速时间设置2.可视 = 真
	快速时间设置3.可视 = 真
结束 过程
	
过程 初始化UI全局()
	
	浏览框1.可视  = 假
	
	本机Y最大值 = 到整数(查询数据库("config", "upEdgeY"))
	本机Y最小值 = 到整数(查询数据库("config", "dowEdgeY"))
	本机X最小值 = 到整数(查询数据库("config", "leftEdgeX"))
	本机X最大值 = 到整数(查询数据库("config", "rightEdgeX"))
	公用模块.弹孔偏移X   = 到整数(查询数据库("config", "bulletOffsetX"))
	公用模块.弹孔偏移Y   = 到整数(查询数据库("config", "bulletOffsetY"))
	
	'默认什么也不调整
	UI调整状态 = 0
	
	'根据分辨率率调整统计表格坐标和大小
	判断 屏幕分辨率
		分支 "1280X800"
			统计表格左边 = 686
			统计表格顶边 = 174
			统计表格宽 = 574
			统计表格高 = 27
			
			高级列表左边 = 689
			高级列表顶边 = 199
			高级列表宽   = 568
			高级列表高   = 219
		分支 "2160X1080"
			统计表格左边 = 1160
			统计表格顶边 = 232
			统计表格宽 = 964
			统计表格高 = 38
			
			高级列表左边 = 1163
			高级列表顶边 = 267
			高级列表宽   = 957
			高级列表高   = 329
		分支 否则
		    统计表格.宽度 = 100
	结束 判断
	
	'获取参数初始化面板
	面板参数 = 查询数据库("config", "panel")
	面板宽度 = 面板参数
	面板高度 = 面板参数	
	
	'获取参数初始化中心点
	靶环中心点X = 查询数据库("config", "coreX")
	靶环中心点Y = 查询数据库("config", "coreY")
	
	'创建调试弹孔
	弹孔0 = 创建 图片框 位于 主窗口
	弹孔0.可视 = 假
	弹孔0.可停留焦点 = 假
	
	'创建射击弹孔
	弹孔1 = 创建 图片框 位于 主窗口
	弹孔1.可视 = 假
	弹孔1.可停留焦点 = 假
	
	弹孔2 = 创建 图片框 位于 主窗口
	弹孔2.可视 = 假
	弹孔2.可停留焦点 = 假
	
	弹孔3 = 创建 图片框 位于 主窗口
	弹孔3.可视 = 假
	弹孔3.可停留焦点 = 假
	
	弹孔4 = 创建 图片框 位于 主窗口
	弹孔4.可视 = 假
	弹孔4.可停留焦点 = 假
	
	弹孔5 = 创建 图片框 位于 主窗口
	弹孔5.可视 = 假
	弹孔5.可停留焦点 = 假
	
	弹孔6 = 创建 图片框 位于 主窗口
	弹孔6.可视 = 假
	弹孔6.可停留焦点 = 假
	
	弹孔7 = 创建 图片框 位于 主窗口
	弹孔7.可视 = 假
	弹孔7.可停留焦点 = 假
	
	弹孔8 = 创建 图片框 位于 主窗口
	弹孔8.可视 = 假
	弹孔8.可停留焦点 = 假
	
	弹孔9 = 创建 图片框 位于 主窗口
	弹孔9.可视 = 假
	弹孔9.可停留焦点 = 假
	
	弹孔10 = 创建 图片框 位于 主窗口
	弹孔10.可视 = 假
	弹孔10.可停留焦点 = 假
	
	弹孔11 = 创建 图片框 位于 主窗口
	弹孔11.可视 = 假
	弹孔11.可停留焦点 = 假
	
	弹孔12 = 创建 图片框 位于 主窗口
	弹孔12.可视 = 假
	弹孔12.可停留焦点 = 假
	
	弹孔13 = 创建 图片框 位于 主窗口
	弹孔13.可视 = 假
	弹孔13.可停留焦点 = 假
	
	弹孔14 = 创建 图片框 位于 主窗口
	弹孔14.可视 = 假
	弹孔14.可停留焦点 = 假
	
	弹孔15 = 创建 图片框 位于 主窗口
	弹孔15.可视 = 假
	弹孔15.可停留焦点 = 假
	
	弹孔16 = 创建 图片框 位于 主窗口
	弹孔16.可视 = 假
	弹孔16.可停留焦点 = 假
	
	弹孔17 = 创建 图片框 位于 主窗口
	弹孔17.可视 = 假
	弹孔17.可停留焦点 = 假
	
	弹孔18 = 创建 图片框 位于 主窗口
	弹孔18.可视 = 假
	弹孔18.可停留焦点 = 假
	
	弹孔19 = 创建 图片框 位于 主窗口
	弹孔19.可视 = 假
	弹孔19.可停留焦点 = 假
	
	弹孔20 = 创建 图片框 位于 主窗口
	弹孔20.可视 = 假
	弹孔20.可停留焦点 = 假
	
	'统计表格
	统计表格.表头背景颜色 = &HFF292E32
	统计表格.分割线颜色 = &HFF16191E
	统计表格.分割线宽度 = 3

	变量 统计表格表头 为 文本型(6)
	统计表格表头(0) = " 射击次数 "
	统计表格表头(1) = " 命中环数 "
	统计表格表头(2) = " 射击间隔 "
	统计表格表头(3) = " 当前精度 "
	统计表格表头(4) = " 射击时间 "
	统计表格表头(5) = " 命中方向 "
	统计表格.添加表头(统计表格表头)
	统计表格.可停留焦点 = 假
	
	统计列表.背景颜色 = &HFF292E32
	统计列表.置分割线颜色(白色)
	统计列表.标题字体颜色 = 白色
	统计列表.可停留焦点 = 假
	
	图片框0.可停留焦点 = 假
	标签0.可停留焦点 = 假
	标题图标.可停留焦点 = 假
	背景2.可停留焦点 = 假
	
	射击序列背景.可停留焦点 = 假
	射击序列描述.可停留焦点 = 假
	射击序列值.可停留焦点 = 假

	命中环数背景.可停留焦点 = 假
	命中环数描述.可停留焦点 = 假
	命中环数值.可停留焦点 = 假

	命中方向背景.可停留焦点 = 假
	命中方向描述.可停留焦点 = 假
	命中方向值.可停留焦点 = 假
	
	射击间隔背景.可停留焦点 = 假
	射击间隔描述.可停留焦点 = 假
	射击间隔值.可停留焦点 = 假

	射击精度背景.可停留焦点 = 假
	射击精度描述.可停留焦点 = 假
	射击精度值.可停留焦点 = 假

	命中总合背景.可停留焦点 = 假
	命中总合描述.可停留焦点 = 假
	命中总合值.可停留焦点 = 假
    
	'设置界面
	上Y约束.可停留焦点 = 假
	下Y约束.可停留焦点 = 假
	左X约束.可停留焦点 = 假
	右X约束.可停留焦点 = 假
	
	面板设置背景.可停留焦点 = 假
	面板设置描述.可停留焦点 = 假
	面板设置值.可停留焦点 = 假
	
	中心点设置背景.可停留焦点 = 假
	中心点设置描述.可停留焦点 = 假
	中心点设置值.可停留焦点 = 假
	
	坐标系Y范围背景.可停留焦点 = 假
	Y范围设置描述.可停留焦点 = 假
	Y范围设置值.可停留焦点 = 假
	
	坐标系X范围背景.可停留焦点 = 假
	X范围设置描述.可停留焦点 = 假
	X范围设置值.可停留焦点 = 假
	
	弹孔偏移背景.可停留焦点 = 假
	弹孔偏移设置描述.可停留焦点 = 假
	弹孔偏移设置值.可停留焦点 = 假
	信息标签.可停留焦点 = 假
	
	终端状态背景.图像 = "terminalStatus.png"
	
	'弹出式高级对话框
	高级对话框1.初始化(面板2,取屏幕宽度(),取屏幕高度())
	
	'2022-02-09
	'初始化历史六枪数组
	历史六枪计数 = 0
	历史六枪数组(0) = 0.0
	历史六枪数组(1) = 0.0
	历史六枪数组(2) = 0.0
	历史六枪数组(3) = 0.0
	历史六枪数组(4) = 0.0
	历史六枪数组(5) = 0.0
	
	历史六枪背景 = 创建 图片框 位于 主窗口
	历史六枪背景.可视 = 真
	历史六枪背景.可停留焦点 = 假
	历史六枪背景.图像 = "6gunHistory.png"
	历史六枪背景.宽度 = 568
	历史六枪背景.高度 = 59
	历史六枪背景.左边 = 689
	历史六枪背景.顶边 = 431
	
	历史一枪记录 = 创建 标签 位于 主窗口
	历史一枪记录.可视 = 真
	历史一枪记录.可停留焦点 = 假
	历史一枪记录.宽度 = 60
	历史一枪记录.高度 = 28
	历史一枪记录.左边 = 711
	历史一枪记录.顶边 = 463
	历史一枪记录.字体大小 = 9
	历史一枪记录.粗体 = 真
	历史一枪记录.标题 = "0.0环"
	历史一枪记录.字体颜色 = 白色
	
	历史二枪记录 = 创建 标签 位于 主窗口
	历史二枪记录.可视 = 真
	历史二枪记录.可停留焦点 = 假
	历史二枪记录.宽度 = 60
	历史二枪记录.高度 = 28
	历史二枪记录.左边 = 790
	历史二枪记录.顶边 = 463
	历史二枪记录.字体大小 = 9
	历史二枪记录.粗体 = 真
	历史二枪记录.标题 = "0.0环"
	历史二枪记录.字体颜色 = 白色
	
	历史三枪记录 = 创建 标签 位于 主窗口
	历史三枪记录.可视 = 真
	历史三枪记录.可停留焦点 = 假
	历史三枪记录.宽度 = 60
	历史三枪记录.高度 = 28
	历史三枪记录.左边 = 870
	历史三枪记录.顶边 = 463
	历史三枪记录.字体大小 = 9
	历史三枪记录.粗体 = 真
	历史三枪记录.标题 = "0.0环"
	历史三枪记录.字体颜色 = 白色
	
	历史四枪记录 = 创建 标签 位于 主窗口
	历史四枪记录.可视 = 真
	历史四枪记录.可停留焦点 = 假
	历史四枪记录.宽度 = 60
	历史四枪记录.高度 = 28
	历史四枪记录.左边 = 950
	历史四枪记录.顶边 = 463
	历史四枪记录.字体大小 = 9
	历史四枪记录.粗体 = 真
	历史四枪记录.标题 = "0.0环"
	历史四枪记录.字体颜色 = 白色
	
	历史五枪记录 = 创建 标签 位于 主窗口
	历史五枪记录.可视 = 真
	历史五枪记录.可停留焦点 = 假
	历史五枪记录.宽度 = 60
	历史五枪记录.高度 = 28
	历史五枪记录.左边 = 1030
	历史五枪记录.顶边 = 463
	历史五枪记录.字体大小 = 9
	历史五枪记录.粗体 = 真
	历史五枪记录.标题 = "0.0环"
	历史五枪记录.字体颜色 = 白色
	
	历史六枪记录 = 创建 标签 位于 主窗口
	历史六枪记录.可视 = 真
	历史六枪记录.可停留焦点 = 假
	历史六枪记录.宽度 = 60
	历史六枪记录.高度 = 28
	历史六枪记录.左边 = 1110
	历史六枪记录.顶边 = 463
	历史六枪记录.字体大小 = 9
	历史六枪记录.粗体 = 真
	历史六枪记录.标题 = "0.0环"
	历史六枪记录.字体颜色 = 白色
	
	历史六枪统计 = 创建 标签 位于 主窗口
	历史六枪统计.可视 = 真
	历史六枪统计.可停留焦点 = 假
	历史六枪统计.宽度 = 60
	历史六枪统计.高度 = 28
	历史六枪统计.左边 = 1190
	历史六枪统计.顶边 = 463
	历史六枪统计.字体大小 = 8
	历史六枪统计.粗体 = 真
	历史六枪统计.标题 = "0.0环"
	历史六枪统计.字体颜色 = 白色
	
	历史靶面1 = 创建 画板 位于 主窗口
	历史靶面1.可视 = 真
	历史靶面1.可停留焦点 = 假
	历史靶面1.画图片("TSB.png",0,0)
	历史靶面1.宽度 = 110
	历史靶面1.高度 = 110
	历史靶面1.左边 = 691
	历史靶面1.顶边 = 497
	
	历史靶面2 = 创建 画板 位于 主窗口
	历史靶面2.可视 = 真
	历史靶面2.可停留焦点 = 假
	历史靶面2.画图片("TSB.png",0,0)
	历史靶面2.宽度 = 110
	历史靶面2.高度 = 110
	历史靶面2.左边 = 804
	历史靶面2.顶边 = 497
	
	历史靶面3 = 创建 画板 位于 主窗口
	历史靶面3.可视 = 真
	历史靶面3.可停留焦点 = 假
	历史靶面3.画图片("TSB.png",0,0)
	历史靶面3.宽度 = 110
	历史靶面3.高度 = 110
	历史靶面3.左边 = 918
	历史靶面3.顶边 = 497
	
	历史靶面4 = 创建 画板 位于 主窗口
	历史靶面4.可视 = 真
	历史靶面4.可停留焦点 = 假
	历史靶面4.画图片("TSB.png",0,0)
	历史靶面4.宽度 = 110
	历史靶面4.高度 = 110
	历史靶面4.左边 = 1031
	历史靶面4.顶边 = 497
	
	历史靶面5 = 创建 画板 位于 主窗口
	历史靶面5.可视 = 真
	历史靶面5.可停留焦点 = 假
	历史靶面5.画图片("TSB.png",0,0)
	历史靶面5.宽度 = 110
	历史靶面5.高度 = 110
	历史靶面5.左边 = 1144
	历史靶面5.顶边 = 497
	
	射击总时背景 = 创建 标签 位于 主窗口
	射击总时背景.可视 = 真
	射击总时背景.可停留焦点 = 假
	射击总时背景.背景颜色 = &HFF292E32
	射击总时背景.宽度 = 240
	射击总时背景.高度 = 71
	射击总时背景.左边 = 691
	射击总时背景.顶边 = 622
	
	射击总时标题 = 创建 标签 位于 主窗口
	射击总时标题.可视 = 真
	射击总时标题.可停留焦点 = 假
	射击总时标题.宽度 = 83
	射击总时标题.高度 = 22
	射击总时标题.左边 = 698
	射击总时标题.顶边 = 652
	射击总时标题.字体大小 = 7
	射击总时标题.粗体 = 真
	射击总时标题.标题 = "射击总时:"
	射击总时标题.字体颜色 = 白色
	
	射击总时时间 = 创建 标签 位于 主窗口
	射击总时时间.可视 = 真
	射击总时时间.可停留焦点 = 假
	射击总时时间.宽度 = 153
	射击总时时间.高度 = 36
	射击总时时间.左边 = 765
	射击总时时间.顶边 = 640
	射击总时时间.字体大小 = 14
	射击总时时间.粗体 = 真
	射击总时时间.标题 = "00:00:00:00"
	射击总时时间.字体颜色 = 白色
	
	快速时间设置背景 = 创建 标签 位于 主窗口
	快速时间设置背景.可视 = 真
	快速时间设置背景.可停留焦点 = 假
	快速时间设置背景.背景颜色 = &HFF292E32
	快速时间设置背景.宽度 = 318
	快速时间设置背景.高度 = 71
	快速时间设置背景.左边 = 936
	快速时间设置背景.顶边 = 622
	
	快速时间设置下拉框 = 创建 下拉列表框 位于 主窗口
	快速时间设置下拉框.可视 = 真
	快速时间设置下拉框.可停留焦点 = 真
	快速时间设置下拉框.添加项目("标准射击")
	快速时间设置下拉框.添加项目("快速射击")
	快速时间设置下拉框.添加项目("女子射击")
	快速时间设置下拉框.添加项目("中心射击")
	快速时间设置下拉框.宽度 = 120
	快速时间设置下拉框.高度 = 40
	快速时间设置下拉框.左边 = 945
	快速时间设置下拉框.顶边 = 637
	
	快速时间设置1 = 创建 按钮 位于 主窗口
	快速时间设置1.可视 = 真
	快速时间设置1.可停留焦点 = 假
	快速时间设置1.图片 = "fastTiming.png"
	快速时间设置1.按下图片 = "fastTiming2.png"
	快速时间设置1.宽度 = 55
	快速时间设置1.高度 = 55
	快速时间设置1.左边 = 1071
	快速时间设置1.顶边 = 629
	快速时间设置1.标题 = "8\n秒"
	快速时间设置1.字体大小 = 8
	快速时间设置1.字体颜色 = 白色
	快速时间设置1.对齐方式 = 靠中居中
	
	快速时间设置2 = 创建 按钮 位于 主窗口
	快速时间设置2.可视 = 真
	快速时间设置2.可停留焦点 = 假
	快速时间设置2.图片 = "fastTiming.png"
	快速时间设置2.按下图片 = "fastTiming2.png"
	快速时间设置2.宽度 = 55
	快速时间设置2.高度 = 55
	快速时间设置2.左边 = 1132
	快速时间设置2.顶边 = 629
	快速时间设置2.标题 = "6\n秒"
	快速时间设置2.字体大小 = 8
	快速时间设置2.字体颜色 = 白色
	快速时间设置2.对齐方式 = 靠中居中
	
	快速时间设置3 = 创建 按钮 位于 主窗口
	快速时间设置3.可视 = 真
	快速时间设置3.可停留焦点 = 假
	快速时间设置3.图片 = "fastTiming.png"
	快速时间设置3.按下图片 = "fastTiming2.png"
	快速时间设置3.宽度 = 55
	快速时间设置3.高度 = 55
	快速时间设置3.左边 = 1194
	快速时间设置3.顶边 = 629
	快速时间设置3.标题 = "4\n秒"
	快速时间设置3.字体大小 = 8
	快速时间设置3.字体颜色 = 白色
	快速时间设置3.对齐方式 = 靠中居中
	
	'2023-11-29 加入学员欢迎标签
	'学员欢迎标签 = 创建 标签 位于 主窗口
	'学员欢迎标签.左边 = 公用模块.取宽度绝对像素(162)
	'学员欢迎标签.顶边 = 公用模块.取高度绝对像素(22)
	'学员欢迎标签.宽度 = 公用模块.取宽度绝对像素(62)
	'学员欢迎标签.高度 = 公用模块.取高度绝对像素(30)
	'学员欢迎标签.字体颜色 = 白色
	'学员欢迎标签.字体大小 = 公用模块.取字体绝对大小(18)
	'学员欢迎标签.标题 = "学员:"
	'学员欢迎标签.对齐方式 = 靠中居中
	
	标签2.背景颜色 = &HFF292E32
	标签3.背景颜色 = &HFF292E32
结束 过程

过程 更新历史六枪记录(命中环数 为 双精度小数型)
	历史六枪计数 = 历史六枪计数 + 1
	判断 历史六枪计数
		分支 1
			历史靶面1.清空()
			历史六枪数组(0) = 命中环数
			历史六枪数组(1) = 0.00
			历史六枪数组(2) = 0.00
			历史六枪数组(3) = 0.00
			历史六枪数组(4) = 0.00
			历史六枪数组(5) = 0.00
			历史一枪记录.标题 = 历史六枪数组(0) & "环"
			历史二枪记录.标题 = 历史六枪数组(1) & "环"
			历史三枪记录.标题 = 历史六枪数组(2) & "环"
			历史四枪记录.标题 = 历史六枪数组(3) & "环"
			历史五枪记录.标题 = 历史六枪数组(4) & "环"
			历史六枪记录.标题 = 历史六枪数组(5) & "环"
			历史靶面1.画图片缩放部分("yxb1280.png",0,0,1280,1280,预瞄转换坐标数组(预瞄射击数据数组成员-2)+公用模块.放大偏移X量+5,预瞄转换坐标数组(预瞄射击数据数组成员-1)+公用模块.放大偏移Y量+5,300,300)
		    历史靶面1.画笔颜色 = 红色
			历史靶面1.画圆(55,55,7)
			历史靶面2.画图片("TSB.png",0,0)
			历史靶面3.画图片("TSB.png",0,0)
			历史靶面4.画图片("TSB.png",0,0)
			历史靶面5.画图片("TSB.png",0,0)
		分支 2
			历史六枪数组(1) = 命中环数
			历史二枪记录.标题 = 历史六枪数组(1) & "环"
			历史靶面2.画图片缩放部分("yxb1280.png",0,0,1280,1280,预瞄转换坐标数组(预瞄射击数据数组成员-2)+公用模块.放大偏移X量+5,预瞄转换坐标数组(预瞄射击数据数组成员-1)+公用模块.放大偏移Y量+5,300,300)
		    历史靶面2.画笔颜色 = 红色
			历史靶面2.画圆(55,55,7)
		分支 3
			历史六枪数组(2) = 命中环数
			历史三枪记录.标题 = 历史六枪数组(2) & "环"
			历史靶面3.画图片缩放部分("yxb1280.png",0,0,1280,1280,预瞄转换坐标数组(预瞄射击数据数组成员-2)+公用模块.放大偏移X量+5,预瞄转换坐标数组(预瞄射击数据数组成员-1)+公用模块.放大偏移Y量+5,300,300)
		    历史靶面3.画笔颜色 = 红色
			历史靶面3.画圆(55,55,7)
		分支 4
			历史六枪数组(3) = 命中环数
			历史四枪记录.标题 = 历史六枪数组(3) & "环"
			历史靶面4.画图片缩放部分("yxb1280.png",0,0,1280,1280,预瞄转换坐标数组(预瞄射击数据数组成员-2)+公用模块.放大偏移X量+5,预瞄转换坐标数组(预瞄射击数据数组成员-1)+公用模块.放大偏移Y量+5,300,300)
		    历史靶面4.画笔颜色 = 红色
			历史靶面4.画圆(55,55,7)
		分支 5
			历史六枪数组(4) = 命中环数
			历史五枪记录.标题 = 历史六枪数组(4) & "环"
			历史靶面5.画图片缩放部分("yxb1280.png",0,0,1280,1280,预瞄转换坐标数组(预瞄射击数据数组成员-2)+公用模块.放大偏移X量+5,预瞄转换坐标数组(预瞄射击数据数组成员-1)+公用模块.放大偏移Y量+5,300,300)
		    历史靶面5.画笔颜色 = 红色
			历史靶面5.画圆(55,55,7)
		分支 6
			历史六枪数组(5) = 命中环数
			历史六枪记录.标题 = 历史六枪数组(5) & "环"
			历史六枪计数 = 0
	结束 判断
	历史六枪统计.标题 = 四舍五入(历史六枪数组(0) + 历史六枪数组(1) + 历史六枪数组(2) + 历史六枪数组(3) + 历史六枪数组(4) + 历史六枪数组(5),1) & "环"
	
结束 过程

事件 快速时间设置下拉框.表项被选择(项目索引 为 整数型)
	公用模块.快速时间设置 = 项目索引
	判断 公用模块.快速时间设置
		分支 0'标准射击
			快速时间设置1.标题 = "150\n秒"
			快速时间设置2.标题 = "20\n秒"
			快速时间设置3.标题 = "10\n秒"
		分支 1'快速射击
			快速时间设置1.标题 = "8\n秒"
			快速时间设置2.标题 = "6\n秒"
			快速时间设置3.标题 = "4\n秒"
		分支 2'女子射击
			快速时间设置1.标题 = "5\n分"
			快速时间设置2.标题 = "2\n分"
			快速时间设置3.标题 = "1\n分"
		分支 3'中心射击
			快速时间设置1.标题 = "20\n分"
			快速时间设置2.标题 = "10\n分"
			快速时间设置3.标题 = "5\n分"
	结束 判断
结束 事件

事件 快速时间设置1.被单击()
	快速时间设置2.图片 = "fastTiming.png"
	快速时间设置3.图片 = "fastTiming.png"
结束 事件
事件 快速时间设置2.被单击()
	快速时间设置1.图片 = "fastTiming.png"
	快速时间设置3.图片 = "fastTiming.png"
结束 事件
事件 快速时间设置3.被单击()
	快速时间设置1.图片 = "fastTiming.png"
	快速时间设置2.图片 = "fastTiming.png"
结束 事件

过程 渲染面板()
	画板1.宽度 = 面板宽度
	清理弹孔按钮.左边 = 面板宽度 - 120
	画板1.高度 = 面板高度
	判断 靶面编号
		分支 "0"
			画板1.背景图片 = "xhb.png"
		分支 "1"
			画板1.背景图片 = "yxb.png"
		分支 "2"
			画板1.背景图片 = "ssyxb.png"
	结束 判断
结束 过程

变量 悬浮按钮状态 为 整数型' 按下=1;抬起=0
过程 渲染校准准星()
	如果 设置开关 = 1 则
	    创建悬浮按钮("",红色,0,无色,"zx.png",靶环中心点X,靶环中心点Y,公用模块.取相对像素(10),公用模块.取相对像素(10))
		如果 悬浮按钮状态 = 0 则
			移动悬浮按钮(靶环中心点X,靶环中心点Y,取悬浮按钮宽度(),取悬浮按钮高度())
		结束 如果
	否则
		销毁悬浮按钮()
	结束 如果
	如果 是否在前台() = 假 则
	    销毁悬浮按钮()
		设置开关 = 0 
	结束 如果
结束 过程
	
事件 主窗口.悬浮按钮被按下()
	'弹出提示("悬浮按钮被按下")
	悬浮按钮状态 = 1
结束 事件

事件 主窗口.悬浮按钮被弹起()
	弹出提示(取悬浮按钮左边() &","& 取悬浮按钮顶边())
	
	靶环中心点X = 取悬浮按钮左边()
	靶环中心点X修改(靶环中心点X)
	
	靶环中心点Y = 取悬浮按钮顶边()
	靶环中心点Y修改(靶环中心点Y)
	
	'恢复状态必须放在最下,之前发现BUG,进行修复：2021-12-30
	悬浮按钮状态 = 0
结束 事件

过程 渲染参数设置()	
	如果 设置开关 = 1 则
		
		'面板设置
	    面板设置背景.背景颜色 = &HFF292E32
	    面板设置背景.可视 = 真
	    面板设置描述.可视 = 真
	    面板设置值.可视 = 真
		面板设置值.标题 = 面板参数
	    面板设置加.可视 = 真
	    面板设置减.可视 = 真
		
		'中心点设置
		中心点设置背景.背景颜色 = &HFF292E32
		中心点设置背景.可视 = 真
		中心点设置描述.可视 = 真
		中心点设置值.可视 = 真
		中心点设置值.标题 = "X:" & 靶环中心点X & "," & "Y:" & 靶环中心点Y
	    靶环中心点X减.可视 = 真
		靶环中心点X加.可视 = 真
		靶环中心点Y减.可视 = 真
		靶环中心点Y加.可视 = 真
	
		'坐标系Y范围
		坐标系Y范围背景.背景颜色 = &HFF292E32
		坐标系Y范围背景.可视 = 真
		Y范围设置描述.可视 = 真
		Y范围设置值.可视 = 真
		Y范围设置值.标题 = "上:" & 本机Y最大值 & "," & "下:" & 本机Y最小值
	    Y范围上减.可视 = 真
		Y范围上加.可视 = 真
		Y范围下减.可视 = 真
		Y范围下加.可视 = 真
		上Y约束.可视 = 真
		上Y约束.宽度 = 本机X最大值 + 取绝对值(本机X最小值)
		上Y约束.左边 = 靶环中心点X + 本机X最小值 + 30
		上Y约束.顶边 = 靶环中心点Y - 本机Y最大值 + 28
		
		下Y约束.可视 = 真
		下Y约束.宽度 = 本机X最大值 + 取绝对值(本机X最小值)
		下Y约束.左边 = 靶环中心点X + 本机X最小值 + 30
		下Y约束.顶边 = 靶环中心点Y - 本机Y最小值 + 30
		
		'坐标系X范围
		坐标系X范围背景.背景颜色 = &HFF292E32
		坐标系X范围背景.可视 = 真
		X范围设置描述.可视 = 真
		X范围设置值.可视 = 真
		Y范围设置值.可视 = 真
		X范围设置值.标题 = "左:" & 本机X最小值 & "," & "右:" & 本机X最大值
	    X范围左减.可视 = 真
		X范围左加.可视 = 真
		X范围右减.可视 = 真
		X范围右加.可视 = 真
		
		左X约束.可视 = 真
		左X约束.高度 = 本机Y最大值 + 取绝对值(本机Y最小值)
		左X约束.顶边 = 靶环中心点Y - 本机Y最大值 + 35
		左X约束.左边 = 靶环中心点X + 本机X最小值 + 27
		
		右X约束.可视 = 真
		右X约束.高度 = 本机Y最大值 + 取绝对值(本机Y最小值)
		右X约束.顶边 = 靶环中心点Y - 本机Y最大值 + 35
		右X约束.左边 = 靶环中心点X + 本机X最大值 + 27
		
		'日志输出
		日志输出.背景颜色 = &HFF292E32
		日志输出.可视 = 真
		
		'如果视觉吧,调整日志高度,留出位置校准放大轨迹
		如果 靶面编号 = "1" 则
			日志输出.高度 = 308
		否则
		    日志输出.高度 = 557
		结束 如果
		
		'创建调试弹孔
		弹孔0.可视 = 真
	    弹孔0.图像 = "dk.png"
	    弹孔0.宽度 = 公用模块.取相对像素(10)
	    弹孔0.高度 = 公用模块.取相对像素(10)
	    弹孔0.左边 = 靶环中心点X + 公用模块.弹孔偏移X
	    弹孔0.顶边 = 靶环中心点Y + 公用模块.弹孔偏移Y
		
		'弹孔偏移
		弹孔偏移背景.背景颜色 = &HFF292E32
		弹孔偏移背景.可视 = 真
		弹孔偏移设置描述.可视 = 真
		弹孔偏移设置值.标题 = "X:" & 公用模块.弹孔偏移X & "," & "Y:" & 公用模块.弹孔偏移Y
		弹孔偏移设置值.可视 = 真
	    弹孔偏移X减.可视 = 真
		弹孔偏移X加.可视 = 真
		弹孔偏移Y减.可视 = 真
		弹孔偏移Y加.可视 = 真
		
		'信息标签
		信息标签.背景颜色 = &HFF292E32
		信息标签.可视 = 真
		信息标签.左边 = 675
		
		'显示调试信息
		调试信息.可视 = 真
		
		'进入UI调整状态输出的调试信息
		如果 UI调整状态 >= 1 且 UI调整状态 <= 2 则
			调试信息.内容 = "统计表格左边:" & 统计表格左边 & "统计表格顶边:" & 统计表格顶边 & "统计表格宽:" & 统计表格宽 &"统计表格高:" & 统计表格高
		结束 如果
		如果 UI调整状态 >= 3 且 UI调整状态 <= 4 则
			调试信息.内容 = "列表左边:" & 高级列表左边 & "列表顶边:" & 高级列表顶边 & "列表宽:" & 高级列表宽 &"列表高:" & 高级列表高
		结束 如果
		
		初始化设置按钮.可视 = 真
		获得校准数据按钮.可视 = 真
		
	否则
		'面板设置
	    面板设置背景.可视 = 假
	    面板设置描述.可视 = 假
	    面板设置值.可视 = 假
	    面板设置加.可视 = 假
	    面板设置减.可视 = 假
		
		'中心点设置
		中心点设置背景.可视 = 假
		中心点设置描述.可视 = 假
		中心点设置值.可视 = 假
	    靶环中心点X减.可视 = 假
		靶环中心点X加.可视 = 假
		靶环中心点Y减.可视 = 假
		靶环中心点Y加.可视 = 假
		
		'坐标系Y范围
		坐标系Y范围背景.可视 = 假
		Y范围设置描述.可视 = 假
		Y范围设置值.可视 = 假
	    Y范围上减.可视 = 假
		Y范围上加.可视 = 假
		Y范围下减.可视 = 假
		Y范围下加.可视 = 假
		上Y约束.可视 = 假
		下Y约束.可视 = 假
		
		'坐标系X范围
		坐标系X范围背景.可视 = 假
		X范围设置描述.可视 = 假
		X范围设置值.可视 = 假
		Y范围设置值.可视 = 假
	    X范围左减.可视 = 假
		X范围左加.可视 = 假
		X范围右减.可视 = 假
		X范围右加.可视 = 假
		左X约束.可视 = 假
		右X约束.可视 = 假
		
		'日志输出
		日志输出.可视 = 假
		
		'销毁调试弹孔
		弹孔0.可视 = 假
		
		'弹孔偏移
		弹孔偏移背景.可视 = 假
		弹孔偏移设置描述.可视 = 假
		弹孔偏移设置值.可视 = 假
	    弹孔偏移X减.可视 = 假
		弹孔偏移X加.可视 = 假
		弹孔偏移Y减.可视 = 假
		弹孔偏移Y加.可视 = 假
		
		'信息标签
		信息标签.可视 = 假
		
		'显示调试信息
		调试信息.可视 = 假
		
		初始化设置按钮.可视 = 假
		获得校准数据按钮.可视 = 假
	结束 如果
结束 过程

过程 渲染射击信息UI()
	如果 设置开关 = 0 则
		'射击序列
		射击序列背景.背景颜色 = &HFF292E32
		射击序列背景.可视 = 真
		射击序列描述.字体颜色 = &HFF30C897
		射击序列描述.可视 = 真
		射击序列值.标题 = 射击序列
		射击序列值.可视 = 真	
		'命中环数
		命中环数背景.背景颜色 = &HFF292E32
		命中环数背景.可视 = 真
		命中环数描述.字体颜色 = &HFFB559F3
		命中环数描述.可视 = 真
		命中环数值.标题 = 命中环数
		命中环数值.可视 = 真
		
		'命中方向
		命中方向背景.背景颜色 = &HFF292E32
		命中方向背景.可视 = 真
		命中方向描述.字体颜色 = &HFFFEC400
		命中方向描述.可视 = 真
		命中方向值.标题 = 命中方向
		命中方向值.可视 = 真		
		
		'射击间隔
		射击间隔背景.背景颜色 = &HFF292E32
		射击间隔背景.可视 = 真
		射击间隔描述.字体颜色 = &HFF3F73EB
		射击间隔描述.可视 = 真
		射击间隔值.标题 = 计时周期
		射击间隔值.可视 = 真		
		'射击精度
		射击精度背景.背景颜色 = &HFF292E32
		射击精度背景.可视 = 真
		射击精度描述.字体颜色 = &HFFFA6779
		射击精度描述.可视 = 真
		射击精度值.标题 = 射击精度统计 & "%"
		射击精度值.可视 = 真		
		'命中总合
		命中总合背景.背景颜色 = &HFF292E32
		命中总合背景.可视 = 真
		命中总合描述.字体颜色 = &HFF30C897
		命中总合描述.可视 = 真
		命中总合值.标题 = 命中总合
		命中总合值.可视 = 真		
		
		'统计表格
		统计表格.可视 = 真

		统计表格.左边 = 统计表格左边
		统计表格.顶边 = 统计表格顶边
		统计表格.宽度 = 统计表格宽
		统计表格.高度 = 统计表格高
		统计表格.置表格行背景色(0,&HFF292E32)
		
		'统计列表
		统计列表.可视 = 真
		统计列表.左边 = 高级列表左边
		统计列表.顶边 = 高级列表顶边
		统计列表.宽度 = 高级列表宽
		统计列表.高度 = 高级列表高
		
		'不在设置窗口内,"开始游戏"按钮显示
		开始游戏按钮.可视 = 真	
		开始游戏按钮.宽度 = 取绝对像素(224)
		开始游戏按钮.高度 = 取绝对像素(48)
	否则
		'射击序列
		射击序列背景.可视 = 假
		射击序列描述.可视 = 假
		射击序列值.可视 = 假
		'命中环数
		命中环数背景.可视 = 假
		命中环数描述.可视 = 假
		命中环数值.可视 = 假
		'命中方向
		命中方向背景.可视 = 假
		命中方向描述.可视 = 假
		命中方向值.可视 = 假	
		'射击间隔
		射击间隔背景.可视 = 假
		射击间隔描述.可视 = 假
		射击间隔值.可视 = 假	
		'射击精度
		射击精度背景.可视 = 假
		射击精度描述.可视 = 假
		射击精度值.可视 = 假	
		'命中总合
		命中总合背景.可视 = 假
		命中总合描述.可视 = 假
		命中总合值.可视 = 假
		
		'统计表格
		统计表格.可视 = 假
		'统计列表
		统计列表.可视 = 假
		
		'开始游戏按钮关闭
	    开始游戏按钮.可视 = 假
	结束 如果
结束 过程

变量 软件时钟1 为 整数型
事件 时钟1.周期事件()
	软件时钟1 = 软件时钟1 + 1
	如果 软件时钟1 > 100 则
		软件时钟1 = 0
	结束 如果
	设置按钮动画()
	WIFI连接状态()
	屏幕保护()
结束 事件

变量 屏保计时 为 整数型
变量 屏保图像 为 图片框
过程 屏幕保护()
	屏保计时 = 屏保计时 + 1
	如果 屏保计时 >= 3600 则
		屏保计时 = 3600
		如果 屏幕亮度 > 0 则
			屏幕亮度 = 屏幕亮度 - 1
		结束 如果
		如果 屏幕亮度 <= 0 则
			屏幕亮度 = 0
			高级对话框1.弹出对话框()
		结束 如果
	结束 如果
	调整屏幕亮度(屏幕亮度)
结束 过程
	
过程 调整屏幕亮度(亮度 为 整数型)
	系统设置1.置屏幕亮度(亮度)
结束 过程
	
过程 设置按钮动画()
	如果 软件时钟1 = 100 则
		校准按钮.旋转特效(0,360,5000,真)
	结束 如果
结束 过程

	变量 WIFI连接中 为 整数型
	变量 WIFI连接状态结果 为 整数型
过程 WIFI连接状态()
	WIFI连接状态结果 = WIFI管理1.取连接状态(WIFI管理1.取当前networkid())
	如果 WIFI管理1.取网关() <> "0.0.0.0" 则
		WIFI连接状态结果 = 0
	结束 如果
    判断 WIFI连接状态结果
        分支 0
            如果 软件时钟1 = 0 或 软件时钟1 = 20 或 软件时钟1 = 40 或 软件时钟1 = 60 或 软件时钟1 = 80 或 软件时钟1 = 100 则
                WIFI状态按钮.图像 = "netYes.png"
            否则
                WIFI状态按钮.图像 = "net.png"
            结束 如果
	    分支 1
			如果 软件时钟1 = 0 或 软件时钟1 = 20 或 软件时钟1 = 40 或 软件时钟1 = 60 或 软件时钟1 = 80 或 软件时钟1 = 100 则
                WIFI状态按钮.图像 = "WiFiHotspot1.png"
            否则
                WIFI状态按钮.图像 = "WiFiHotspot0.png"
            结束 如果
        分支 2
            WIFI连接中 = WIFI连接中 + 1
            如果 WIFI连接中 < 5 则
                WIFI状态按钮.图像 = "net.png"
            结束 如果
            如果 WIFI连接中 > 5 则
                WIFI状态按钮.图像 = "netunknown.png"
	        结束 如果
			如果 WIFI连接中 > 10 则
	            WIFI连接中 = 0
            结束 如果
        分支 3
            WIFI状态按钮.图像 = "netunknown.png"
    结束 判断   
结束 过程
	
过程 刷新UI全局()
	渲染面板()
	渲染校准准星()
	渲染参数设置()
	渲染射击信息UI()
	处理预瞄UI()
结束 过程

	'语音选择
变量 语音进入状态 为 逻辑型
事件 语音选择男.选择改变()
	如果 语音选择男.选中 = 真 则
		语音选择男.选中 = 真
		语音选择女.选中 = 假
		语音选择关.选中 = 假
		语音结果 = "男"
		修改数据库("config", "voice", "value",语音结果)
		查询音效结果()
		如果 语音进入状态 = 真 则
			播放音乐("man/10.0.wav")
		结束 如果
	结束 如果
结束 事件
事件 语音选择女.选择改变()
	如果 语音选择女.选中 = 真 则
		语音选择男.选中 = 假
		语音选择女.选中 = 真
		语音选择关.选中 = 假
		语音结果 = "女"
		修改数据库("config", "voice", "value",语音结果)
		查询音效结果()
		如果 语音进入状态 = 真 则
			播放音乐("woman/10.0.wav")
		结束 如果
	结束 如果
结束 事件
事件 语音选择关.选择改变()
	如果 语音选择关.选中 = 真 则
		语音选择男.选中 = 假
		语音选择女.选中 = 假
		语音选择关.选中 = 真
		语音结果 = "关"
		修改数据库("config", "voice", "value",语音结果)
		查询音效结果()
	结束 如果
结束 事件

'弹孔叠加
事件 弹孔堆叠开.选择改变()
	如果 弹孔堆叠开.选中 = 真 则
		弹孔堆叠开.选中 = 真
		弹孔堆叠关.选中 = 假
		弹孔叠加结果 = "开"
		修改数据库("config", "bulletStack", "value",弹孔叠加结果)
		查询弹孔叠加结果()
	结束 如果
结束 事件
事件 弹孔堆叠关.选择改变()
	如果 弹孔堆叠关.选中 = 真 则
		弹孔堆叠开.选中 = 假
		弹孔堆叠关.选中 = 真
		弹孔叠加结果 = "关"
		修改数据库("config", "bulletStack", "value",弹孔叠加结果)
		查询弹孔叠加结果()
	结束 如果
结束 事件

'自动结束
事件 自动结束开.选择改变()
	如果 自动结束开.选中 = 真 则
		自动结束开.选中 = 真
		自动结束关.选中 = 假
		自动结束结果 = "开"
		修改数据库("config", "autoend", "value",自动结束结果)
		查询自动结束结果()
	结束 如果
结束 事件
事件 自动结束关.选择改变()
	如果 自动结束关.选中 = 真 则
		自动结束开.选中 = 假
		自动结束关.选中 = 真
		自动结束结果 = "关"
		修改数据库("config", "autoend", "value",自动结束结果)
		查询自动结束结果()
	结束 如果
结束 事件

'射击音效
事件 射击音效开.选择改变()
	如果 射击音效开.选中 = 真 则
		射击音效开.选中 = 真
		射击音效关.选中 = 假
		射击音效结果 = "开"
		修改数据库("config", "shootingSound", "value",射击音效结果)
		查询射击音效结果()
	结束 如果
结束 事件
事件 射击音效关.选择改变()
	如果 射击音效关.选中 = 真 则
		射击音效开.选中 = 假
		射击音效关.选中 = 真
		射击音效结果 = "关"
		修改数据库("config", "shootingSound", "value",射击音效结果)
		查询射击音效结果()
	结束 如果
结束 事件

'上弹音效
事件 上弹音效开.选择改变()
	如果 上弹音效开.选中 = 真 则
		上弹音效开.选中 = 真
		上弹音效关.选中 = 假
		上弹音效结果 = "开"
		修改数据库("config", "topPlaySound", "value",上弹音效结果)
		查询上弹音效结果()
	结束 如果
结束 事件
事件 上弹音效关.选择改变()
	如果 上弹音效关.选中 = 真 则
		上弹音效开.选中 = 假
		上弹音效关.选中 = 真
		上弹音效结果 = "关"
		修改数据库("config", "topPlaySound", "value",上弹音效结果)
		查询上弹音效结果()
	结束 如果
结束 事件

'切换靶面
事件 靶面选择0.选择改变()
	如果 靶面选择0.选中 = 真 则
		靶面选择0.选中 = 真
		靶面选择1.选中 = 假
		靶面选择2.选中 = 假
		靶面编号 = "0"
		修改数据库("config", "target", "value",靶面编号)
		查询靶面编号结果()
		预瞄渲染开.选中 = 假
		预瞄渲染关.选中 = 真
		公用模块.预瞄渲染结果 = "关"
		修改数据库("config", "previewRender", "value",公用模块.预瞄渲染结果)
		查询预瞄渲染结果()
	结束 如果
结束 事件
事件 靶面选择1.选择改变()
	如果 靶面选择1.选中 = 真 则
		靶面选择0.选中 = 假
		靶面选择1.选中 = 真
		靶面选择2.选中 = 假
		靶面编号 = "1"
		修改数据库("config", "target", "value",靶面编号)
		查询靶面编号结果()
		
		'公用模块.预瞄渲染结果 = "开"
		'修改数据库("config", "previewRender", "value",公用模块.预瞄渲染结果)
		'查询预瞄渲染结果()
		'预瞄渲染开.选中 = 真
	结束 如果
结束 事件
事件 靶面选择2.选择改变()
	如果 靶面选择2.选中 = 真 则
		靶面选择0.选中 = 假
		靶面选择1.选中 = 假
		靶面选择2.选中 = 真
		靶面编号 = "2"
		修改数据库("config", "target", "value",靶面编号)
		查询靶面编号结果()
	结束 如果
结束 事件

	变量 开始计时     为 双精度小数型
	变量 射击总计时   为 双精度小数型
	变量 射击总计分   为 双精度小数型
	变量 射击总计秒   为 双精度小数型
	变量 射击总计秒十 为 双精度小数型
事件 射击间隔时钟.周期事件()
	开始计时 = 开始计时 + 0.1
	开始计时 = 四舍五入(开始计时,1) 
	
	如果 开始计时 > 3.0 则
		计时周期 = 开始计时 - 3.0
		游戏开始状态 = 真
		开始游戏按钮.可用 = 真
	结束 如果
	
	如果 开始计时 > 12.8 则
		开始计时 = 12.8
	结束 如果
	计时周期 = 四舍五入(计时周期,1) 
	射击间隔值.标题 = 计时周期
	'2022-02-11:加入对射击总计时的处理
	射击总计秒十 = 射击总计秒十 + 0.1
	如果 射击总计秒十 >= 1.0 则
		射击总计秒十 = 0.0
		射击总计秒 = 射击总计秒 + 0.1
	结束 如果
	如果 射击总计秒 >= 5.9 则
		射击总计秒 = 0.0
		射击总计分 = 射击总计分 + 0.1
	结束 如果
	如果 射击总计分 >= 5.9 则
		射击总计分 = 0.0
		射击总计时 = 射击总计时 + 0.1
	结束 如果
	如果 射击总计时 >= 2.4 则
		射击总计时 = 0.0
		射击总计分 = 0.0
		射击总计秒 = 0.0
	结束 如果
	如果 公用模块.预瞄渲染结果 = "关" 则 
		变量 时 为 文本型
		变量 分 为 文本型
		变量 秒 为 文本型
		变量 秒十 为 文本型
		时 = 子文本替换(到文本(四舍五入(射击总计时,1)),".","")
		分 = 子文本替换(到文本(四舍五入(射击总计分,1)),".","")
		秒 = 子文本替换(到文本(四舍五入(射击总计秒,1)),".","")
		秒十 = 子文本替换(到文本(四舍五入(射击总计秒十,1)),".","")
		射击总时时间.标题 = 时 & ":" & 分 & ":" & 秒 & ":" & 秒十
	结束 如果
结束 事件

事件 服务器1.客户进入(IP地址 为 文本型,端口 为 整数型)
	弹出提示("客户端进入：" & IP地址)
	客户端连接成功 = 真
	WIFI客户端成功 = 真
	客户端001地址 = IP地址
	客户端001端口 = 端口
	公用模块.校准状态 = 假
结束 事件

事件 终端检测时钟.周期事件()
	公用模块.桥连接状态超时 = 公用模块.桥连接状态超时 - 1
	如果 公用模块.桥连接状态超时 < 0 则
		公用模块.桥连接状态超时 = 0
		终端1状态.图像 = "bridgeRedState0.png"
		公用模块.桥连接状态 = 假
	结束 如果

	公用模块.靶连接状态超时 = 公用模块.靶连接状态超时 - 1
	如果 公用模块.靶连接状态超时 < 0 则
		公用模块.靶连接状态超时 = 0
		终端2状态.图像 = "targetRedState0.png"
		公用模块.靶连接状态 = 假
	结束 如果

	公用模块.枪连接状态超时 = 公用模块.枪连接状态超时 - 1
	如果 公用模块.枪连接状态超时 < 0 则
		公用模块.枪连接状态超时 = 0
		终端3状态.图像 = "gunRedState0.png"
		公用模块.枪连接状态 = 假
	结束 如果
结束 事件

事件 服务器1.客户离开(IP地址 为 文本型,端口 为 整数型)
	弹出提示("客户端掉线：" & IP地址)
	如果 客户端连接成功 = 真 且 IP地址 = 客户端001地址 则
		客户端连接成功 = 假
		WIFI客户端成功 = 假
		客户端001地址 = ""
		客户端001端口 = ""
	结束 如果
结束 事件

事件 数据报1.收到数据(数据 为 字节型(),IP地址 为 文本型,端口 为 整数型)
	WIFI客户端成功 =真
	接收信息处理(数据,IP地址,端口)
	'弹出提示(字节到文本(数据,"UTF-8"))
结束 事件

事件 服务器1.收到数据(数据 为 字节型(),IP地址 为 文本型,端口 为 整数型)
	WIFI客户端成功 =真
	接收信息处理(数据,IP地址,端口)
结束 事件


事件 串口通讯1.收到数据(数据 为 字节型())
	
	变量 字节转文本 为 文本型
	字节转文本 = 字节转文本 & 字节到文本(数据,"UTF-8")
	日志输出处理("串口接收:", 字节转文本)
	删首尾空(字节转文本)	
	
	变量 终端信息 为 文本型
	终端信息 = 取指定文本2(字节转文本,"@state:",";")
	
	如果 终端信息 <> "" 则
		变量 JSON对象 为 对象
		JSON对象 = JSON操作1.解析(终端信息)
		变量 回复信息 为 文本型
		回复信息 = "@state:{\"result\":\"ok\"};\r\n"
		'日志输出处理("串口发送:", 回复信息)
		串口通讯1.发送数据(文本到字节(回复信息,"UTF-8"))
		
		变量 枪状态 为 文本型
		枪状态 = JSON操作1.取文本值(JSON对象,"gun")
		变量 靶状态 为 文本型
		靶状态 = JSON操作1.取文本值(JSON对象,"target")
		
		如果 枪状态 <> "" 且 靶状态 <> "" 则
			公用模块.桥连接状态 = 真
			公用模块.桥连接状态超时 = 3
			终端1状态.图像 = "bridgeRedState1.png"
		结束 如果
		
		如果 靶面编号 = "0" 则
			接收信息处理(数据,"0.0.0.0",0)
		结束 如果
	结束 如果
结束 事件

变量 字节转文本 为 文本型
变量 终端信息 为 文本型
变量 计次 为 整数型	
变量 统计 为 整数型
变量 射击结束标记 为 逻辑型
变量 预瞄转换坐标数组 为 文本型(600)
变量 预瞄射击数据数组成员 为 整数型

过程 接收信息处理(数据 为 字节型(),IP地址 为 文本型,端口 为 整数型)
	
	'字节转文本 = 删首尾空(字节转文本 & 字节到文本(数据,"UTF-8"))
	'弹出提示(字节转文本)
	'退出
	
	字节转文本 = 删首尾空(字节转文本 & 字节到文本(数据,"UTF-8"))
	如果 设置开关 = 1 则
		'日志输出处理("接收信息:", 字节转文本)	    
	结束 如果
	
	'终端状态信息
	终端信息 = 取指定文本2(字节转文本,"@state:",";")	
	如果 终端信息 <> "" 则
		'弹出提示(终端信息)
		'日志输出处理("接收信息:", 终端信息)
		变量 JSON对象 为 对象
		JSON对象 = JSON操作1.解析(终端信息)
		变量 回复信息 为 文本型
		回复信息 = "@state:{\"result\":\"ok\"};\r\n"
		'日志输出处理("发送信息:", 回复信息)
		如果 靶面编号 = "1" 则
			服务器1.发送数据(文本到字节(回复信息,"UTF-8"),IP地址,端口)
			如果 公用模块.校准状态 = 假 则
				公用模块.校准状态 = 真
				'获取物理靶面的校准数据
				校准数据时钟.时钟周期 = 1000
				公用模块.校准等待延迟 = 0
			结束 如果
		结束 如果
		
		变量 靶状态 为 文本型
		靶状态 = JSON操作1.取文本值(JSON对象,"target")
		
		变量 枪状态 为 文本型
		枪状态 = JSON操作1.取文本值(JSON对象,"gun")
		
		如果 靶状态 = "1" 则
			公用模块.靶连接状态 = 真
			公用模块.靶连接状态超时 = 3
			终端2状态.图像 = "targetRedState1.png"
		结束 如果
		
		如果 枪状态 = "1" 则
			公用模块.枪连接状态 = 真
			公用模块.枪连接状态超时 = 3
			终端3状态.图像 = "gunRedState1.png"
		结束 如果
		
		如果 枪状态 <> "" 且 靶状态 <> "" 则
			公用模块.桥连接状态 = 真
			公用模块.桥连接状态超时 = 3
			终端1状态.图像 = "bridgeRedState1.png"
		结束 如果
		
		字节转文本 = 子文本替换(字节转文本,"@state:"& 终端信息 & ";","")
		终端信息 = ""
	结束 如果
	
	'########################一代靶环
	'只有在设置页面或者游戏开始才正式处理射击信息
	'频点设置完成
	终端信息 = 取指定文本2(字节转文本,"@channel:",";")
	如果 终端信息 <> "" 则
		变量 JSON对象 为 对象
		JSON对象 = JSON操作1.解析(终端信息)
		'日志输出处理("接收信息:", 终端信息)
		如果 JSON操作1.取文本值(JSON对象,"gun") = 0 则
			弹出提示("枪支的频点设置错误,请尝试重新设置!")
		结束 如果
		如果 JSON操作1.取文本值(JSON对象,"target") = 0 则
			弹出提示("靶面频点设置错误,请尝试重新设置!")
		结束 如果
		如果 JSON操作1.取文本值(JSON对象,"gun") = 1 且 JSON操作1.取文本值(JSON对象,"target") = 1 则
			弹出提示("频点设置成功")
		结束 如果
		字节转文本 = 子文本替换(字节转文本,"@channel:"& 终端信息 & ";","")
		终端信息 = ""
	结束 如果
	
	如果 设置开关 = 1 或 游戏开始状态 = 真 则
		'拉栓上弹
		终端信息 = 取指定文本2(字节转文本,"@clip:",";")
		如果 终端信息 <> "" 则
			'弹出提示(终端信息)
			日志输出处理("接收信息:", 终端信息)
			变量 JSON对象 为 对象
			JSON对象 = JSON操作1.解析(终端信息)
			变量 回复信息 为 文本型
			回复信息 = "@clip:{\"result\":\"ok\"};"
			日志输出处理("发送信息:", 回复信息)
			'弹出提示(回复信息)
			如果 WIFI客户端成功 = 真 则
		        服务器1.发送数据(文本到字节(回复信息,"UTF-8"),IP地址,端口)
		    否则
		        串口通讯1.发送数据(文本到字节(回复信息,"UTF-8"))
		    结束 如果
			如果 上弹音效结果 = "开" 则
	            播放音乐("gun/goOn"& 上弹音效值结果 &".mp3")
	        结束 如果
			字节转文本 = 子文本替换(字节转文本,"@clip:"& 终端信息 & ";","")
			终端信息 = ""
			恢复屏幕亮度()
		结束 如果
	
		'射击信息
		终端信息 = 取指定文本2(字节转文本,"@shooting:",";")
		如果 终端信息 <> "" 则
			'弹出提示(终端信息)
			日志输出处理("接收信息:", 终端信息)
			退出
			变量 JSON对象 为 对象
			JSON对象 = JSON操作1.解析(终端信息)
			变量 回复信息 为 文本型
			回复信息 = "@shooting:{\"result\":\"ok\"};"
			日志输出处理("发送信息:", 回复信息)
			'弹出提示(回复信息)
			如果 WIFI客户端成功 = 真 则
		        服务器1.发送数据(文本到字节(回复信息,"UTF-8"),IP地址,端口)
		    否则
		        串口通讯1.发送数据(文本到字节(回复信息,"UTF-8"))
		    结束 如果
			变量 X射击坐标 为 文本型
			变量 Y射击坐标 为 文本型
			变量 射击环数 为 文本型
			X射击坐标 = JSON操作1.取文本值(JSON对象,"x")
			Y射击坐标 = JSON操作1.取文本值(JSON对象,"y")
			射击环数 =  JSON操作1.取文本值(JSON对象,"h")
			射击信息处理(X射击坐标, Y射击坐标, 射击环数)
			恢复屏幕亮度()
			如果 射击音效结果 = "开" 则
                播放音乐("gun/shooting"& 射击音效值结果 &".mp3")
	        结束 如果
			字节转文本 = 子文本替换(字节转文本,"@shooting:"& 终端信息 & ";","")
			终端信息 = ""
			恢复屏幕亮度()
		结束 如果
	结束 如果
	
	
	'########################二代靶环
	
	'预瞄圆形靶
	'收到终端校准信息
	'校准数据格式信息：
	'[Xcenter(中心X),Ycenter(中心Y),XSmall(最小X),XLarge(最大X),YSmall(最小Y),YLarge(最大Y)]
	终端信息 = 取指定文本2(字节转文本,"@calibration:",";")
	如果 终端信息 <> "" 则	
		'弹出提示(终端信息)
		修改数据库("config", "calibration", "value",BASE64编码(文本到字节(终端信息,"UTF-8")))
		查询视觉靶校准数据结果()
		弹出提示(视觉靶校准数据)
		字节转文本 = 子文本替换(字节转文本,"@calibration:"& 终端信息 & ";","")
		终端信息 = ""
	结束 如果
		
	如果 取指定文本2(字节转文本,"@shoot:",";") <> "" 且 射击结束标记 = 假 则
		字节转文本 = 子文本替换(字节转文本,"@shoot:"& 取指定文本2(字节转文本,"@shoot:",";") & ";","")
		字节转文本 = ""
		射击结束标记 = 真
		报送结束时钟.时钟周期 = 500
	结束 如果
	
	'如果 射击结束标记 = 真 则
		
		变量 清理信息 为 文本型
		终端信息 = 取指定文本2(字节转文本,"@optics:",";")
		清理信息 = 终端信息
		弹出提示(终端信息)
		'弹出提示(寻找文本(终端信息,"[",0))
		'弹出提示(寻找文本(终端信息,"]",0))
		'弹出提示(寻找文本(终端信息,",",0))
	    
		'判断是否选了自动结束,如果是,丢弃数据
		如果 公用模块.开始的结束 = 真 则
			终端信息 = "错误"
		结束 如果
		
		'判断中括号
		如果 寻找文本(终端信息,"[",0) = -1 或 寻找文本(终端信息,"]",6) = -1 则
			终端信息 = "错误"
		结束 如果
		
		'过滤数据,如果格式不符合条件,则丢弃,防止APP崩溃
		'如果 寻找文本(终端信息,",",3) = -1 且 寻找文本(终端信息,",",9) 则
		'	终端信息 = "错误"
		'结束 如果
		
		'数据正确,进行数据处理
		如果 终端信息 <> "错误" 则
			
			'整理数据
			终端信息 = 子文本替换(终端信息,"[","")
			终端信息 = 子文本替换(终端信息,"]","")
			终端信息 = 子文本替换(终端信息,"\"","")
			终端信息 = 子文本替换(终端信息,"*,","")
			终端信息 = 子文本替换(终端信息,"*","")
			终端信息 = 删首尾空(终端信息)

			'如果结尾有都好,删除
			如果 取文本右边(终端信息,1) = "," 则
				终端信息 = 取文本左边(终端信息,取文本长度2(终端信息)-1)
			否则
			    终端信息 = 取文本左边(终端信息,取文本长度2(终端信息))
			结束 如果

			'开始将机械视觉坐标转换成靶面坐标
			变量 预瞄射击数据数组 为 文本型(600)
			预瞄射击数据数组 = 分割文本(终端信息,",")
			'弹出提示(取数组成员数(预瞄射击数据数组))

			公用模块.恢复画板初始值()
			画板1.清空()
			回放时钟.时钟周期 = 0

			变量 预瞄坐标数组转换 为 文本型(600)		

			变量 待存预瞄射击数据 为 文本型
			待存预瞄射击数据 = ""

			变量 坐标转换计次 为 整数型
			坐标转换计次 = 0

			预瞄射击数据数组成员 = 取数组成员数(预瞄射击数据数组)
			
			如果 预瞄射击数据数组成员 > 2 则
				
				射击结束标记 = 假
		
			    判断循环首 坐标转换计次 < 预瞄射击数据数组成员
				    变量 转换预瞄坐标结果 为 文本型
					转换预瞄坐标结果 = 计算预瞄坐标系(预瞄射击数据数组[坐标转换计次],预瞄射击数据数组[坐标转换计次+1])
					变量 分割预瞄转换坐标结果 为 文本型(2)
					分割预瞄转换坐标结果 = 分割文本(转换预瞄坐标结果,",")
					预瞄转换坐标数组(坐标转换计次) = 分割预瞄转换坐标结果(0)
					预瞄转换坐标数组(坐标转换计次+1) = 分割预瞄转换坐标结果(1)
					待存预瞄射击数据 = 待存预瞄射击数据 & 转换预瞄坐标结果 & ","
					坐标转换计次 = 坐标转换计次 + 2
				判断循环尾

				公用模块.透 = 255
				画板1.画笔颜色 = 到颜色值(公用模块.透,公用模块.红,公用模块.绿,公用模块.蓝)
				画板1.画图片缩放("dk"&公用模块.射击计数 + 1&".png",预瞄转换坐标数组(预瞄射击数据数组成员-2) + 公用模块.弹孔偏移X,预瞄转换坐标数组(预瞄射击数据数组成员-1) + 公用模块.弹孔偏移Y,公用模块.取相对像素(10),公用模块.取相对像素(10))
				'存储射击历史记录
				待存预瞄射击数据 = 取文本左边(待存预瞄射击数据,取文本长度2(待存预瞄射击数据)-1)
				公用模块.预瞄射击历史数据组(公用模块.射击计数) = 待存预瞄射击数据
	            '缩放小窗口
				预瞄轨迹画板.画图片缩放部分("yxb1280.png",0,0,1280,1280,预瞄转换坐标数组(预瞄射击数据数组成员-2)+公用模块.放大偏移X量,预瞄转换坐标数组(预瞄射击数据数组成员-1)+公用模块.放大偏移Y量,300,300)

				公用模块.恢复画板初始值()
				查询预瞄渲染结果()
				公用模块.回放预瞄射击轨迹()

				'计算靶环方向\环数\语音和统计信息等
				射击信息处理2(预瞄转换坐标数组(预瞄射击数据数组成员-2),预瞄转换坐标数组(预瞄射击数据数组成员-1))
		
		        公用模块.回放计次 = 0
				公用模块.射击计数 = 公用模块.射击计数 + 1
				公用模块.射击计数统计 = 公用模块.射击计数统计 + 1
				公用模块.列表序号 = 公用模块.列表序号 + 1
				公用模块.回放指针 = 公用模块.射击计数
				回放标记标签.标题 = "回放记录:" & 公用模块.回放指针
				如果 公用模块.射击计数 > 19 则
				    公用模块.射击计数 = 0
					公用模块.列表序号 = 1
					公用模块.循环标记 = 真
				否则
				    公用模块.循环标记 = 假
				结束 如果
		
			结束 如果
	        字节转文本 = 子文本替换(字节转文本,"@optics:"& 清理信息 & ";","")
			终端信息 = ""
			恢复屏幕亮度()
		否则
		    字节转文本 = 子文本替换(字节转文本,"@optics:"& 清理信息 & ";","")
			终端信息 = ""
		结束 如果
	'结束 如果
	
	'防止溢出
	如果 取文本长度(字节转文本) > 3072 则
		字节转文本 = ""
	结束 如果
结束 过程

事件 报送结束时钟.周期事件()
	变量 脱吧数据 为 文本型
	脱吧数据 = "@optics:[255,255],[255,255],[255,255],[255,255],[255,255],[*],[*];"
	接收信息处理(文本到字节(脱吧数据,"UTF-8"),"0.0.0.0",0)
	报送结束时钟.时钟周期 = 0
	射击结束标记 = 假
结束 事件

变量 画板X中心点坐标 为 双精度小数型
变量 画板Y中心点坐标 为 双精度小数型

函数 计算预瞄坐标系(X基值 为 整数型,Y基值 为 整数型) 为 文本型
	'弹出提示("X基值:" & X基值 & ",Y基值:" & Y基值)
	'弹出提示(视觉靶校准数据)
	
	变量 校准数据JSON对象 为 对象
	'校准数据[Xcenter(中心X),Ycenter(中心Y),XSmall(最小X),XLarge(最大X),YSmall(最小Y),YLarge(最大Y)]
	校准数据JSON对象 = JSON操作1.解析(视觉靶校准数据)
	变量 物理X中心点 为 文本型
	变量 物理Y中心点 为 文本型	
	变量 物理边界X最小值 为 整数型
	变量 物理边界X最大值 为 整数型
	变量 物理边界Y最小值 为 整数型
	变量 物理边界Y最大值 为 整数型
	物理X中心点 = JSON操作1.取文本值(校准数据JSON对象,"Xcenter")
	物理Y中心点 = JSON操作1.取文本值(校准数据JSON对象,"Ycenter")
	物理边界X最小值 = JSON操作1.取文本值(校准数据JSON对象,"XSmall")
	物理边界X最大值 = JSON操作1.取文本值(校准数据JSON对象,"XLarge")
	物理边界Y最小值 = JSON操作1.取文本值(校准数据JSON对象,"YSmall")
	物理边界Y最大值 = JSON操作1.取文本值(校准数据JSON对象,"YLarge")
	
	'####当前计数物理靶面坐标系数
	'通过基础值减掉中心点,大于0则在右测,小于0在左侧
	变量 物理X转换坐标 为 双精度小数型
	变量 物理Y转换坐标 为 双精度小数型
	物理X转换坐标 = X基值 - 物理X中心点
	物理Y转换坐标 = Y基值 - 物理Y中心点
	'弹出提示("物理X转换坐标:" & 物理X转换坐标 & ",物理Y转换坐标:" & 物理Y转换坐标)	
	
	'计算X比例基础放大系数
	'弹出提示("本机X最小值:" & 本机X最小值 & ",本机X最大值:" & 本机X最大值)
	变量 X物理放大系数 为 双精度小数型
	如果 物理X转换坐标 = 0 则
		X物理放大系数 = 0
	否则
	   	如果 物理X转换坐标 < 0 则
		    X物理放大系数 = 取绝对值(本机X最小值) / (物理X中心点 - 物理边界X最小值)
	   	否则
		    X物理放大系数 = 本机X最大值 / (物理边界X最大值 - 物理X中心点)
	   	结束 如果
	结束 如果
	
	'计算Y比例基础放大系数
	变量 Y物理放大系数 为 双精度小数型
	如果 物理Y转换坐标 = 0 则
		Y物理放大系数 = 0
	否则
	    如果 物理Y转换坐标 < 0 则
		    Y物理放大系数 = 本机Y最大值 / (物理Y中心点 - 物理边界Y最小值)
	    否则
			Y物理放大系数 = 取绝对值(本机Y最小值) / (物理边界Y最大值 - 物理Y中心点)
	    结束 如果
	    '弹出提示("本机Y最小值:" & 本机Y最小值 & ",本机Y最大值:" & 本机Y最大值)
	结束 如果
	
	画板X中心点坐标 = 靶环中心点X - 画板1.左边
	画板Y中心点坐标 = 靶环中心点Y - 画板1.顶边
	'弹出提示("画板1.左边:" & 画板1.左边 & ",画板1.顶边:" & 画板1.顶边)
	
	变量 预瞄换算X结果 为 双精度小数型
	变量 预瞄换算Y结果 为 双精度小数型
	预瞄换算X结果 = 画板X中心点坐标 + 物理X转换坐标 * X物理放大系数
	预瞄换算Y结果 = 画板Y中心点坐标 + 物理Y转换坐标 * Y物理放大系数
	计算预瞄坐标系 = 四舍五入(预瞄换算X结果,0) &","&四舍五入(预瞄换算Y结果,0)
	'弹出提示(四舍五入(预瞄换算X结果,0))
	'弹出提示(四舍五入(预瞄换算Y结果,0))
结束 函数

事件 回放时钟.周期事件()
	启动回放轨迹()
结束 事件

过程 启动回放轨迹()
	线程1.唤醒线程()
	线程1.线程处理过程("run")
结束 过程

'对二代AI视觉靶做的匹配方法
过程 射击信息处理2(坐标X 为 文本型,坐标Y 为 文本型)
	
	变量 物理X转换坐标 为 双精度小数型
	变量 物理Y转换坐标 为 双精度小数型
	物理X转换坐标 = 坐标X - 画板X中心点坐标
	物理Y转换坐标 = 坐标Y - 画板Y中心点坐标
	'弹出提示("物理X转换坐标:" & 物理X转换坐标 & ",物理Y转换坐标:" & 物理Y转换坐标)
	
	变量 靶环方向X 为 文本型
    变量 靶环方向Y 为 文本型
	变量 中文靶环方向 为 文本型
	变量 英文靶环方向 为 文本型
	
	如果 物理X转换坐标 = 0 则
		靶环方向X = ""
	结束 如果
	如果 物理X转换坐标 < 0 则
		靶环方向X = "left"
	结束 如果
	如果 物理X转换坐标 > 0 则
		靶环方向X = "right"
	结束 如果
	
	如果 物理Y转换坐标 = 0 则
		靶环方向Y = ""
	结束 如果
	如果 物理Y转换坐标 < 0 则
		靶环方向Y = "up"
	结束 如果
	如果 物理Y转换坐标 > 0 则
		靶环方向Y = "dow"
	结束 如果
	
	如果 靶环方向Y = "up" 且 靶环方向X = "" 则
		中文靶环方向 = "上"
		英文靶环方向 = "up"
	结束 如果
	如果 靶环方向Y = "up" 且 靶环方向X = "right" 则
		中文靶环方向 = "右上"
		英文靶环方向 = "rightup"
	结束 如果
	如果 靶环方向Y = "" 且 靶环方向X = "right" 则
		中文靶环方向 = "右"
		英文靶环方向 = "right"
	结束 如果
	如果 靶环方向Y = "dow" 且 靶环方向X = "right" 则
		中文靶环方向 = "右下"
		英文靶环方向 = "rightdown"
	结束 如果
	如果 靶环方向Y = "dow" 且 靶环方向X = "" 则
		中文靶环方向 = "下"
		英文靶环方向 = "down"
	结束 如果
	如果 靶环方向Y = "dow" 且 靶环方向X = "left" 则
		中文靶环方向 = "左下"
		英文靶环方向 = "leftdown"
	结束 如果
	如果 靶环方向Y = "" 且 靶环方向X = "left" 则
		中文靶环方向 = "左"
		英文靶环方向 = "left"
	结束 如果
		如果 靶环方向Y = "up" 且 靶环方向X = "left" 则
		中文靶环方向 = "左上"
		英文靶环方向 = "leftup"
	结束 如果
	'弹出提示(靶环方向X)
	'弹出提示(靶环方向Y)
	
	变量 斜边X 为 双精度小数型
	变量 斜边Y 为 双精度小数型
	变量 中心点坐标距离 为 双精度小数型
	'斜边X = 取绝对值(物理X中心点-坐标X) * 取绝对值(物理X中心点-坐标X)
	'斜边Y = 取绝对值(物理Y中心点-坐标Y) * 取绝对值(物理Y中心点-坐标Y)
	'中心点坐标距离 = 求平方根(斜边X + 斜边Y)
	
	斜边X = 取绝对值(画板X中心点坐标-坐标X) * 取绝对值(画板X中心点坐标-坐标X)
	斜边Y = 取绝对值(画板Y中心点坐标-坐标Y) * 取绝对值(画板Y中心点坐标-坐标Y)
	中心点坐标距离 = 求平方根(斜边X + 斜边Y)
	'中心点坐标距离 = 四舍五入(求平方根(斜边X + 斜边Y),2)	
	
	'弹出提示("平方根:" & 中心点坐标距离)
	
	如果 射击音效结果 = "开" 则
        播放音乐("gun/shooting"& 射击音效值结果 &".mp3")
	结束 如果
	
	如果 中心点坐标距离 >= 0.00 且 中心点坐标距离 <= 19.00 则

		如果 中心点坐标距离 >= 0.00 且 中心点坐标距离 < 2.00 则
			命中环数 = 10.9
		结束 如果
		如果 中心点坐标距离 >= 2.00 且 中心点坐标距离 < 4.00 则
			命中环数 = 10.8
		结束 如果
		如果 中心点坐标距离 >= 4.00 且 中心点坐标距离 < 6.00  则
			命中环数 = 10.7
		结束 如果
		如果 中心点坐标距离 >= 6.00 且 中心点坐标距离 < 8.00 则
			命中环数 = 10.6
		结束 如果
		如果 中心点坐标距离 >= 8.00 且 中心点坐标距离 < 10.00 则
			命中环数 = 10.5
		结束 如果
		如果 中心点坐标距离 >= 10.00 且 中心点坐标距离 < 12.00 则
			命中环数 = 10.4
		结束 如果
		如果 中心点坐标距离 >= 12.00 且 中心点坐标距离 < 14.00 则
			命中环数 = 10.3
		结束 如果
		如果 中心点坐标距离 >= 14.00 且 中心点坐标距离 < 16.00 则
			命中环数 = 10.2
		结束 如果
		如果 中心点坐标距离 >= 16.00 且 中心点坐标距离 < 18.00 则
			命中环数 = 10.1
		结束 如果
		如果 中心点坐标距离 >= 18.00 且 中心点坐标距离 <= 19.00 则
			命中环数 = 10.0
		结束 如果
		
		中文靶环方向 = "中心"
		英文靶环方向 = "core"
	结束 如果
	
	如果 中心点坐标距离 > 19.00 且 中心点坐标距离 <= 43.00 则

		如果 中心点坐标距离 > 19.00 且 中心点坐标距离 <= 21.40 则
			命中环数 = 9.9
		结束 如果
		如果 中心点坐标距离 > 21.40 且 中心点坐标距离 <= 23.80 则
			命中环数 = 9.8
		结束 如果
		如果 中心点坐标距离 > 23.80 且 中心点坐标距离 <= 26.20  则
			命中环数 = 9.7
		结束 如果
		如果 中心点坐标距离 > 26.20 且 中心点坐标距离 <= 28.60 则
			命中环数 = 9.6
		结束 如果
		如果 中心点坐标距离 > 28.60 且 中心点坐标距离 <= 31.00 则
			命中环数 = 9.5
		结束 如果
		如果 中心点坐标距离 > 31.00 且 中心点坐标距离 <= 33.40 则
			命中环数 = 9.4
		结束 如果
		如果 中心点坐标距离 > 33.40 且 中心点坐标距离 <= 35.80 则
			命中环数 = 9.3
		结束 如果
		如果 中心点坐标距离 > 35.80 且 中心点坐标距离 <= 38.20 则
			命中环数 = 9.2
		结束 如果
		如果 中心点坐标距离 > 38.20 且 中心点坐标距离 <= 40.60 则
			命中环数 = 9.1
		结束 如果
		如果 中心点坐标距离 > 40.60 且 中心点坐标距离 <= 43.00 则
			命中环数 = 9.0
		结束 如果

	结束 如果
	
	如果 中心点坐标距离 > 43.00 且 中心点坐标距离 <= 74.00 则

		如果 中心点坐标距离 > 43.00 且 中心点坐标距离 <= 46.10 则
			命中环数 = 8.9
		结束 如果
		如果 中心点坐标距离 > 46.10 且 中心点坐标距离 <= 49.20 则
			命中环数 = 8.8
		结束 如果
		如果 中心点坐标距离 > 49.20 且 中心点坐标距离 <= 52.30  则
			命中环数 = 8.7
		结束 如果
		如果 中心点坐标距离 > 52.30 且 中心点坐标距离 <= 55.40 则
			命中环数 = 8.6
		结束 如果
		如果 中心点坐标距离 > 55.40 且 中心点坐标距离 <= 58.50 则
			命中环数 = 8.5
		结束 如果
		如果 中心点坐标距离 > 58.50 且 中心点坐标距离 <= 61.60 则
			命中环数 = 8.4
		结束 如果
		如果 中心点坐标距离 > 61.60 且 中心点坐标距离 <= 64.70 则
			命中环数 = 8.3
		结束 如果
		如果 中心点坐标距离 > 64.70 且 中心点坐标距离 <= 67.80 则
			命中环数 = 8.2
		结束 如果
		如果 中心点坐标距离 > 67.80 且 中心点坐标距离 <= 70.90 则
			命中环数 = 8.1
		结束 如果
		如果 中心点坐标距离 > 70.90 且 中心点坐标距离 <= 74.00 则
			命中环数 = 8.0
		结束 如果

	结束 如果
	
	如果 中心点坐标距离 > 74.00 且 中心点坐标距离 <= 104.00 则

		如果 中心点坐标距离 > 74.00 且 中心点坐标距离 <= 77.00 则
			命中环数 = 7.9
		结束 如果
		如果 中心点坐标距离 > 77.00 且 中心点坐标距离 <= 80.00 则
			命中环数 = 7.8
		结束 如果
		如果 中心点坐标距离 > 80.00 且 中心点坐标距离 <= 83.00  则
			命中环数 = 7.7
		结束 如果
		如果 中心点坐标距离 > 83.00 且 中心点坐标距离 <= 86.00 则
			命中环数 = 7.6
		结束 如果
		如果 中心点坐标距离 > 86.00 且 中心点坐标距离 <= 89.00 则
			命中环数 = 7.5
		结束 如果
		如果 中心点坐标距离 > 89.00 且 中心点坐标距离 <= 92.00 则
			命中环数 = 7.4
		结束 如果
		如果 中心点坐标距离 > 92.00 且 中心点坐标距离 <= 95.00 则
			命中环数 = 7.3
		结束 如果
		如果 中心点坐标距离 > 95.00 且 中心点坐标距离 <= 98.00 则
			命中环数 = 7.2
		结束 如果
		如果 中心点坐标距离 > 98.00 且 中心点坐标距离 <= 101.00 则
			命中环数 = 7.1
		结束 如果
		如果 中心点坐标距离 > 101.00 且 中心点坐标距离 <= 104.00 则
			命中环数 = 7.0
		结束 如果

	结束 如果
	
	如果 中心点坐标距离 > 104.00 且 中心点坐标距离 <= 135.00 则

		如果 中心点坐标距离 > 104.00 且 中心点坐标距离 <= 107.10 则
			命中环数 = 6.9
		结束 如果
		如果 中心点坐标距离 > 107.10 且 中心点坐标距离 <= 110.20 则
			命中环数 = 6.8
		结束 如果
		如果 中心点坐标距离 > 110.20 且 中心点坐标距离 <= 113.30  则
			命中环数 = 6.7
		结束 如果
		如果 中心点坐标距离 > 113.30 且 中心点坐标距离 <= 116.40 则
			命中环数 = 6.6
		结束 如果
		如果 中心点坐标距离 > 116.40 且 中心点坐标距离 <= 119.50 则
			命中环数 = 6.5
		结束 如果
		如果 中心点坐标距离 > 119.50 且 中心点坐标距离 <= 122.60 则
			命中环数 = 6.4
		结束 如果
		如果 中心点坐标距离 > 122.60 且 中心点坐标距离 <= 125.70 则
			命中环数 = 6.3
		结束 如果
		如果 中心点坐标距离 > 125.70 且 中心点坐标距离 <= 128.80 则
			命中环数 = 6.2
		结束 如果
		如果 中心点坐标距离 > 128.80 且 中心点坐标距离 <= 131.90 则
			命中环数 = 6.1
		结束 如果
		如果 中心点坐标距离 > 131.90 且 中心点坐标距离 <= 135.00 则
			命中环数 = 6.0
		结束 如果

	结束 如果
	
	如果 中心点坐标距离 > 135.00 且 中心点坐标距离 <= 164.40 则

		如果 中心点坐标距离 > 135.00 且 中心点坐标距离 <= 137.90 则
			命中环数 = 5.9
		结束 如果
		如果 中心点坐标距离 > 137.90 且 中心点坐标距离 <= 140.80 则
			命中环数 = 5.8
		结束 如果
		如果 中心点坐标距离 > 140.80 且 中心点坐标距离 <= 143.70  则
			命中环数 = 5.7
		结束 如果
		如果 中心点坐标距离 > 143.70 且 中心点坐标距离 <= 146.60 则
			命中环数 = 5.6
		结束 如果
		如果 中心点坐标距离 > 146.60 且 中心点坐标距离 <= 149.50 则
			命中环数 = 5.5
		结束 如果
		如果 中心点坐标距离 > 149.50 且 中心点坐标距离 <= 152.40 则
			命中环数 = 5.4
		结束 如果
		如果 中心点坐标距离 > 152.40 且 中心点坐标距离 <= 155.30 则
			命中环数 = 5.3
		结束 如果
		如果 中心点坐标距离 > 155.30 且 中心点坐标距离 <= 158.20 则
			命中环数 = 5.2
		结束 如果
		如果 中心点坐标距离 > 158.20 且 中心点坐标距离 <= 161.10 则
			命中环数 = 5.1
		结束 如果
		如果 中心点坐标距离 > 161.10 且 中心点坐标距离 <= 164.40 则
			命中环数 = 5.0
		结束 如果

	结束 如果
	
	如果 中心点坐标距离 > 164.4 且 中心点坐标距离 <= 196.00 则

		如果 中心点坐标距离 > 164.40 且 中心点坐标距离 <= 167.56 则
			命中环数 = 4.9
		结束 如果
		如果 中心点坐标距离 > 167.56 且 中心点坐标距离 <= 170.72 则
			命中环数 = 4.8
		结束 如果
		如果 中心点坐标距离 > 170.72 且 中心点坐标距离 <= 173.88  则
			命中环数 = 4.7
		结束 如果
		如果 中心点坐标距离 > 173.88 且 中心点坐标距离 <= 177.04 则
			命中环数 = 4.6
		结束 如果
		如果 中心点坐标距离 > 177.04 且 中心点坐标距离 <= 180.20 则
			命中环数 = 4.5
		结束 如果
		如果 中心点坐标距离 > 180.20 且 中心点坐标距离 <= 183.36 则
			命中环数 = 4.4
		结束 如果
		如果 中心点坐标距离 > 183.36 且 中心点坐标距离 <= 186.52 则
			命中环数 = 4.3
		结束 如果
		如果 中心点坐标距离 > 186.52 且 中心点坐标距离 <= 189.68 则
			命中环数 = 4.2
		结束 如果
		如果 中心点坐标距离 > 189.68 且 中心点坐标距离 <= 192.84 则
			命中环数 = 4.1
		结束 如果
		如果 中心点坐标距离 > 192.84 且 中心点坐标距离 <= 196.00 则
			命中环数 = 4.0
		结束 如果

	结束 如果
	
	如果 中心点坐标距离 > 196.00 且 中心点坐标距离 <= 225.14 则

		如果 中心点坐标距离 > 196.00 且 中心点坐标距离 <= 198.91 则
			命中环数 = 3.9
		结束 如果
		如果 中心点坐标距离 > 198.91 且 中心点坐标距离 <= 201.82 则
			命中环数 = 3.8
		结束 如果
		如果 中心点坐标距离 > 201.82 且 中心点坐标距离 <= 204.73  则
			命中环数 = 3.7
		结束 如果
		如果 中心点坐标距离 > 204.73 且 中心点坐标距离 <= 207.64 则
			命中环数 = 3.6
		结束 如果
		如果 中心点坐标距离 > 207.64 且 中心点坐标距离 <= 210.55 则
			命中环数 = 3.5
		结束 如果
		如果 中心点坐标距离 > 210.55 且 中心点坐标距离 <= 213.46 则
			命中环数 = 3.4
		结束 如果
		如果 中心点坐标距离 > 213.46 且 中心点坐标距离 <= 216.37 则
			命中环数 = 3.3
		结束 如果
		如果 中心点坐标距离 > 216.37 且 中心点坐标距离 <= 219.28 则
			命中环数 = 3.2
		结束 如果
		如果 中心点坐标距离 > 219.28 且 中心点坐标距离 <= 222.19 则
			命中环数 = 3.1
		结束 如果
		如果 中心点坐标距离 > 222.19 且 中心点坐标距离 <= 225.14 则
			命中环数 = 3.0
		结束 如果

	结束 如果
	
	如果 中心点坐标距离 > 225.14 且 中心点坐标距离 <= 256.00 则

		如果 中心点坐标距离 > 225.14 且 中心点坐标距离 <= 228.14 则
			命中环数 = 2.9
		结束 如果
		如果 中心点坐标距离 > 228.14 且 中心点坐标距离 <= 231.20 则
			命中环数 = 2.8
		结束 如果
		如果 中心点坐标距离 > 231.20 且 中心点坐标距离 <= 234.30  则
			命中环数 = 2.7
		结束 如果
		如果 中心点坐标距离 > 234.30 且 中心点坐标距离 <= 237.40 则
			命中环数 = 2.6
		结束 如果
		如果 中心点坐标距离 > 237.40 且 中心点坐标距离 <= 240.50 则
			命中环数 = 2.5
		结束 如果
		如果 中心点坐标距离 > 240.50 且 中心点坐标距离 <= 243.60 则
			命中环数 = 2.4
		结束 如果
		如果 中心点坐标距离 > 243.60 且 中心点坐标距离 <= 246.70 则
			命中环数 = 2.3
		结束 如果
		如果 中心点坐标距离 > 246.70 且 中心点坐标距离 <= 249.80 则
			命中环数 = 2.2
		结束 如果
		如果 中心点坐标距离 > 249.80 且 中心点坐标距离 <= 252.90 则
			命中环数 = 2.1
		结束 如果
		如果 中心点坐标距离 > 252.90 且 中心点坐标距离 <= 256.00 则
			命中环数 = 2.0
		结束 如果

	结束 如果
	
	如果 中心点坐标距离 > 256.00 且 中心点坐标距离 <= 287.00 则

		如果 中心点坐标距离 > 256.00 且 中心点坐标距离 <= 259.02 则
			命中环数 = 1.9
		结束 如果
		如果 中心点坐标距离 > 259.02 且 中心点坐标距离 <= 262.04 则
			命中环数 = 1.8
		结束 如果
		如果 中心点坐标距离 > 262.04 且 中心点坐标距离 <= 265.06  则
			命中环数 = 1.7
		结束 如果
		如果 中心点坐标距离 > 265.06 且 中心点坐标距离 <= 268.08 则
			命中环数 = 1.6
		结束 如果
		如果 中心点坐标距离 > 268.08 且 中心点坐标距离 <= 271.10 则
			命中环数 = 1.5
		结束 如果
		如果 中心点坐标距离 > 271.10 且 中心点坐标距离 <= 274.12 则
			命中环数 = 1.4
		结束 如果
		如果 中心点坐标距离 > 274.12 且 中心点坐标距离 <= 277.14 则
			命中环数 = 1.3
		结束 如果
		如果 中心点坐标距离 > 277.14 且 中心点坐标距离 <= 280.16 则
			命中环数 = 1.2
		结束 如果
		如果 中心点坐标距离 > 280.16 且 中心点坐标距离 <= 283.18 则
			命中环数 = 1.1
		结束 如果
		如果 中心点坐标距离 > 283.18 且 中心点坐标距离 <= 287.00 则
			命中环数 = 1.0
		结束 如果

	结束 如果
	
	如果 中心点坐标距离 > 287.00 则
		命中环数 = 0.0
		中文靶环方向 = "脱靶"
	结束 如果
	
	'########整理环数和方向并播放语音信息
	'处理环数
	变量 环数双精数 为 双精度小数型
	变量 分割环数 为 文本型()
    环数双精数 = 到数值(命中环数)
    分割环数 =  分割文本(到文本(环数双精数),".")
	如果 靶环方向Y = "" 且 靶环方向X = "" 则
		公用模块.报环类型 = 0
		公用模块.靶环环数 = 命中环数
	否则
	    公用模块.报环类型 = 1
		公用模块.靶环环数 = 命中环数
		公用模块.英文靶环方向 = 英文靶环方向
		公用模块.播报流程 = 0
	结束 如果
	
	如果 中文靶环方向 = "脱靶" 则
		公用模块.报环类型 = 2
		公用模块.靶环环数 = 0.0
		公用模块.英文靶环方向 = 英文靶环方向
		公用模块.播报流程 = 0
	结束 如果
	
	播放音效时钟.时钟周期 = 500
	
	'####################开始处理统计信息
	'单次命中环数
	变量 单次环数 为 文本型
	如果 命中环数 = 10.0 则
		单次环数 = "10"
	否则
	    单次环数 = 命中环数
	结束 如果
	
	'2022-02-14
	更新历史六枪记录(命中环数)
	
	'射击序列
	射击序列 = 公用模块.射击计数统计
	命中方向 = 中文靶环方向
	
	'射击间隔
	变量 射击间隔 为 文本型
	射击间隔 = 计时周期 & "0"
	
	'单次射击精度
	变量 射击精度百分比 为 整数型
	变量 精度百分比 为 文本型(2)
	射击精度百分比 = 命中环数  * 10
	如果 射击精度百分比 <= 0 则
		精度百分比(0) = 0
	否则
	    精度百分比 = 分割文本(到文本(射击精度百分比),".")
	结束 如果
	
	'计算射击精度损耗和统计射击精度
	变量 射击精度损耗 为 整数型
	射击精度损耗 = 射击精度最大百分比 - 到整数(精度百分比(0))
	射击精度损耗统计 = 射击精度损耗统计 + 射击精度损耗
	射击精度统计 = 射击精度最大百分比 - (射击精度损耗统计 / 公用模块.射击计数统计)
	射击精度统计 = 四舍五入(射击精度统计,2)
	
	命中总合 = 命中总合 + 命中环数
	命中总合 = 四舍五入(命中总合,1)
	
	'修订时间格式
	变量 月份修订 为 文本型
	变量 日修订 为 文本型
	变量 小时修订 为 文本型
	变量 分钟修订 为 文本型
	变量 秒修订 为 文本型
	
	如果 取月份(取现行时间()) < 10 则
		月份修订 = "0" & 取月份(取现行时间())
	否则
		月份修订 = 取月份(取现行时间())
	结束 如果
	
	如果 取日(取现行时间()) < 10 则
		日修订 = "0" & 取日(取现行时间())
	否则
		日修订 = 取日(取现行时间())
	结束 如果
	
	如果 取小时(取现行时间())  < 10 则
		小时修订 = "0" & 取小时(取现行时间())
	否则
	    小时修订 = 取小时(取现行时间())
	结束 如果
	
    如果 取分钟(取现行时间())  < 10 则
		分钟修订 = "0" & 取分钟(取现行时间())
	否则
	    分钟修订 = 取分钟(取现行时间())
	结束 如果
	
    如果 取秒(取现行时间())  < 10 则
		秒修订 = "0" & 取秒(取现行时间())
	否则
	    秒修订 = 取秒(取现行时间())
	结束 如果
	
	射击时间 = 取年份(取现行时间()) & "-" & 月份修订 & "-" & 日修订  & " " & 小时修订 & ":" & 分钟修订 & ":" & 秒修订
	
	'显示修正统计列表中的精度百分比
	如果 精度百分比(0) = 0 则
		精度百分比(0) = "0" & 精度百分比(0) & ".00"
	结束 如果
	如果 精度百分比(0) > 0 且 精度百分比(0) < 100 则
		精度百分比(0) = 精度百分比(0) & ".00"
	结束 如果
	如果 精度百分比(0) >= 100 则
		精度百分比(0) = 100 & ".0"
	结束 如果
	
	'根据分辨率修正射击时间的位置
	变量 时间空格分隔 为 文本型
	判断 屏幕分辨率
		分支 "1280X800"
            时间空格分隔 = "　　　　　　　　"
		分支 "2160X1080"
            时间空格分隔 = "                   "
		分支 否则
		    时间空格分隔 = "                     "
	结束 判断
	
	'修正单次环数显示
	如果 单次环数 < 10 则
		单次环数 = 单次环数 & "0"
	结束 如果
	
	如果 单次环数 = 10 则
		单次环数 = 单次环数 & ".0"
	结束 如果
	
	'向统计列表添加速度并滚动到当前行
	统计列表.添加项目("xl" & 公用模块.列表序号 & ".png","　　"& 单次环数 &"　　　 "& 射击间隔 &"　　 "& 精度百分比(0) &"%"& 时间空格分隔 & 射击时间 & "","",英文靶环方向 & ".png","")
	统计列表.置现行选中项(统计列表.取项目数())
	开始计时 = 3.0
	计时周期 = 0
	
	'射击次数已满
	如果 公用模块.射击计数统计 >= 自动结束值结果 且 游戏开始状态 = 真 则
		如果 自动结束结果 = "开" 且 设置开关 = 0 则
			播放音乐("gameend.mp3")
			游戏开始状态 = 假
			公用模块.开始的结束 = 真
			开始计时 = 0
			射击间隔时钟.时钟周期 = 0
			开始游戏按钮.标题 = "开始"
		结束 如果
	结束 如果

	渲染射击信息UI()
结束 过程
	
过程 射击信息处理(坐标X 为 文本型,坐标Y 为 文本型,环数 为 文本型)
    '弹出提示(坐标X &","& 坐标Y &","&环数)
	' 定义计算常亮
	变量 传输Y最大值 为 整数型
	变量 传输Y最小值 为 整数型
	变量 传输X最大值 为 整数型
	变量 传输X最小值 为 整数型
	传输Y最大值 = 60
	传输Y最小值 = -40
	传输X最大值 = 50
	传输X最小值 = -50
	
	'累计射击次数
	公用模块.射击计数 = 公用模块.射击计数 + 1
	
    变量 靶环方向X 为 文本型
    变量 靶环方向Y 为 文本型
    变量 弹孔X坐标 为 整数型
	变量 弹孔Y坐标 为 整数型
	变量 中文靶环方向 为 文本型
	变量 英文靶环方向 为 文本型
	
	'计算传输的坐标X并判断射击方向
	如果 坐标X > 0 则
		变量 X轴比例系数计算 为 双精度小数型
		X轴比例系数计算 = 本机X最大值 / 传输X最大值
		坐标X = 坐标X * X轴比例系数计算
		靶环方向X = "right"
	结束 如果
	如果 坐标X = 0 则
		靶环方向X = ""
	结束 如果
	如果 坐标X < 0 则
	    变量 X轴比例系数计算 为 双精度小数型
		变量 绝对值转换 为 双精度小数型
		X轴比例系数计算 = 取绝对值(本机X最小值) / 取绝对值(传输X最小值)
		绝对值转换 = 取绝对值(坐标X) * X轴比例系数计算
		坐标X = (绝对值转换 - 绝对值转换) - 绝对值转换
		靶环方向X = "left"
	结束 如果

	'计算传输的坐标Y并判断射击方向
	如果 坐标Y > 0 则
		变量 Y轴比例系数计算 为 双精度小数型
		Y轴比例系数计算 = 本机Y最大值 / 传输Y最大值
		坐标Y = 坐标Y * Y轴比例系数计算
		靶环方向Y = "up"
	结束 如果
	如果 坐标Y = 0 则
		靶环方向Y = ""
	结束 如果
	如果 坐标Y < 0 则
	    变量 Y轴比例系数 为 双精度小数型
		变量 绝对值转换 为 双精度小数型
		Y轴比例系数 = 取绝对值(本机Y最小值) / 取绝对值(传输Y最小值)
		绝对值转换 = 取绝对值(坐标Y) * Y轴比例系数
		坐标Y = (绝对值转换 - 绝对值转换) - 绝对值转换
		靶环方向Y = "dow"
	结束 如果
	
	'处理环数
	变量 环数双精数 为 双精度小数型
	变量 分割环数 为 文本型()

    环数双精数 = 到数值(环数)
    分割环数 =  分割文本(到文本(环数双精数),".")
	
	'弹出提示(环数双精数)
	
	'转换把环信息为特定形式
	如果 靶环方向Y = "" 且 靶环方向X = "" 则
		如果 环数双精数 = 0.0 则
			中文靶环方向 = "脱靶"
		    英文靶环方向 = "miss"
		否则
	        中文靶环方向 = "中心"
		    英文靶环方向 = "core"
		结束 如果
	结束 如果
	如果 靶环方向Y = "up" 且 靶环方向X = "" 则
		中文靶环方向 = "上"
		英文靶环方向 = "up"
	结束 如果
	如果 靶环方向Y = "up" 且 靶环方向X = "right" 则
		中文靶环方向 = "右上"
		英文靶环方向 = "rightup"
	结束 如果
	如果 靶环方向Y = "" 且 靶环方向X = "right" 则
		中文靶环方向 = "右"
		英文靶环方向 = "right"
	结束 如果
	如果 靶环方向Y = "dow" 且 靶环方向X = "right" 则
		中文靶环方向 = "右下"
		英文靶环方向 = "rightdown"
	结束 如果
	如果 靶环方向Y = "dow" 且 靶环方向X = "" 则
		中文靶环方向 = "下"
		英文靶环方向 = "down"
	结束 如果
	如果 靶环方向Y = "dow" 且 靶环方向X = "left" 则
		中文靶环方向 = "左下"
		英文靶环方向 = "leftdown"
	结束 如果
	如果 靶环方向Y = "" 且 靶环方向X = "left" 则
		中文靶环方向 = "左"
		英文靶环方向 = "left"
	结束 如果
		如果 靶环方向Y = "up" 且 靶环方向X = "left" 则
		中文靶环方向 = "左上"
		英文靶环方向 = "leftup"
	结束 如果
	'弹出提示(靶环方向X)
	'弹出提示(靶环方向Y)
	
	'根据环数播放音效
	如果 分割环数(0) = "0" 且 分割环数(1) = "0" 则
		播放音效 = 环数双精数 &".mp3"
	否则
	    如果 分割环数(1) = "0" 则
		    播放音效 = 分割环数(0) & 英文靶环方向 &".mp3"
	    否则
		    播放音效 = 环数双精数 &".mp3"
	    结束 如果
	结束 如果
	播放音效时钟.时钟周期 = 500
	'弹出提示(播放音效)
	
	'处理单孔信息
	如果 环数双精数 <> "0.0" 则
		弹孔X坐标 = 靶环中心点X + 坐标X + 公用模块.弹孔偏移X
		弹孔Y坐标 = 靶环中心点Y - 坐标Y + 公用模块.弹孔偏移Y
		处理弹孔(公用模块.射击计数,弹孔X坐标, 弹孔Y坐标)
	结束 如果
	
	'单次命中环数
	变量 单次环数 为 文本型
	如果 环数双精数 = 10.0 则
		单次环数 = "10"
	否则
	    单次环数 = 环数双精数
	结束 如果
	
	'统计信息
	射击序列 = 公用模块.射击计数
	命中环数 = 环数双精数
	命中方向 = 中文靶环方向
	
	变量 射击间隔 为 文本型
	射击间隔 = 计时周期 & "0"
	
	'单次射击精度
	变量 射击精度百分比 为 整数型
	变量 精度百分比 为 文本型(2)
	射击精度百分比 = 环数双精数  * 10
	如果 射击精度百分比 <=0 则
		精度百分比(0) = 0
	否则
	    精度百分比 = 分割文本(到文本(射击精度百分比),".")
	结束 如果
	
	'计算射击精度损耗和统计射击精度
	变量 射击精度损耗 为 整数型
	射击精度损耗 = 射击精度最大百分比 - 到整数(精度百分比(0))
	射击精度损耗统计 = 射击精度损耗统计 + 射击精度损耗
	射击精度统计 = 射击精度最大百分比 - (射击精度损耗统计 / 公用模块.射击计数)
	射击精度统计 = 四舍五入(射击精度统计,2)
	
	命中总合 = 命中总合 + 命中环数
	命中总合 = 四舍五入(命中总合,1)
	
	'修订时间格式
	变量 月份修订 为 文本型
	变量 日修订 为 文本型
	变量 小时修订 为 文本型
	变量 分钟修订 为 文本型
	变量 秒修订 为 文本型
	
	如果 取月份(取现行时间()) < 10 则
		月份修订 = "0" & 取月份(取现行时间())
	否则
		月份修订 = 取月份(取现行时间())
	结束 如果
	
	如果 取日(取现行时间()) < 10 则
		日修订 = "0" & 取日(取现行时间())
	否则
		日修订 = 取日(取现行时间())
	结束 如果
	
	如果 取小时(取现行时间())  < 10 则
		小时修订 = "0" & 取小时(取现行时间())
	否则
	    小时修订 = 取小时(取现行时间())
	结束 如果
	
    如果 取分钟(取现行时间())  < 10 则
		分钟修订 = "0" & 取分钟(取现行时间())
	否则
	    分钟修订 = 取分钟(取现行时间())
	结束 如果
	
    如果 取秒(取现行时间())  < 10 则
		秒修订 = "0" & 取秒(取现行时间())
	否则
	    秒修订 = 取秒(取现行时间())
	结束 如果
	
	射击时间 = 取年份(取现行时间()) & "-" & 月份修订 & "-" & 日修订  & " " & 小时修订 & ":" & 分钟修订 & ":" & 秒修订
	
	'显示修正统计列表中的精度百分比
	如果 精度百分比(0) = 0 则
		精度百分比(0) = "0" & 精度百分比(0) & ".00"
	结束 如果
	如果 精度百分比(0) > 0 且 精度百分比(0) < 100 则
		精度百分比(0) = 精度百分比(0) & ".00"
	结束 如果
	如果 精度百分比(0) >= 100 则
		精度百分比(0) = 100 & ".0"
	结束 如果
	
	'根据分辨率修正射击时间的位置
	变量 时间空格分隔 为 文本型
	判断 屏幕分辨率
		分支 "1280X800"
            时间空格分隔 = "　　　　　　　　"
		分支 "2160X1080"
            时间空格分隔 = "                   "
		分支 否则
		    时间空格分隔 = "                     "
	结束 判断
	
	'修正单次环数显示
	如果 单次环数 < 10 则
		单次环数 = 单次环数 & "0"
	结束 如果
	
	如果 单次环数 = 10 则
		单次环数 = 单次环数 & ".0"
	结束 如果
	
	'向统计列表添加速度并滚动到当前行
	统计列表.添加项目("xl" & 公用模块.射击计数 & ".png","　　"& 单次环数 &"　　　 "& 射击间隔 &"　　 "& 精度百分比(0) &"%"& 时间空格分隔 & 射击时间 & "","",英文靶环方向 & ".png","")
	统计列表.置现行选中项(统计列表.取项目数())
	开始计时 = 3.0
	计时周期 = 0
	
	'射击次数已满
	如果 公用模块.射击计数 = 自动结束值结果 则
		如果 自动结束结果 = "开" 且 设置开关 = 0 则
			公用模块.射击计数 = 0
			播放音乐("gameend.mp3")
			游戏开始状态 = 假
			开始计时 = 0
			射击间隔时钟.时钟周期 = 0
			开始游戏按钮.标题 = "开始"
		否则
			射击精度损耗统计 = 射击精度损耗统计 / 公用模块.射击计数
			公用模块.射击计数 = 1
		结束 如果
	结束 如果
	
结束 过程

过程 日志输出处理(标题 为 文本型, 内容 为 文本型)
	日志输出.添加项目(标题 & 内容)
	日志输出.置现行选中项(日志输出.取项目数())
结束 过程

过程 处理弹孔(射击次数 为 整数型 ,弹孔X坐标 为 整数型, 弹孔Y坐标 为 整数型)
	如果 弹孔叠加结果 = "关" 则
		清理弹孔()
	结束 如果
	判断 射击次数
		分支 1
			弹孔1.可视 = 真
			弹孔1.图像 = "dk1.png"
			弹孔1.宽度 = 公用模块.取相对像素(10)
			弹孔1.高度 = 公用模块.取相对像素(10)
			弹孔1.左边 = 弹孔X坐标
			弹孔1.顶边 = 弹孔Y坐标
		分支 2
			弹孔2.可视 = 真
			弹孔2.图像 = "dk2.png"
			弹孔2.宽度 = 公用模块.取相对像素(10)
			弹孔2.高度 = 公用模块.取相对像素(10)
			弹孔2.左边 = 弹孔X坐标
			弹孔2.顶边 = 弹孔Y坐标
		分支 3
			弹孔3.可视 = 真
			弹孔3.图像 = "dk3.png"
			弹孔3.宽度 = 公用模块.取相对像素(10)
			弹孔3.高度 = 公用模块.取相对像素(10)
			弹孔3.左边 = 弹孔X坐标
			弹孔3.顶边 = 弹孔Y坐标
		分支 4
			弹孔4.可视 = 真
			弹孔4.图像 = "dk4.png"
			弹孔4.宽度 = 公用模块.取相对像素(10)
			弹孔4.高度 = 公用模块.取相对像素(10)
			弹孔4.左边 = 弹孔X坐标
			弹孔4.顶边 = 弹孔Y坐标
		分支 5
			弹孔5.可视 = 真
			弹孔5.图像 = "dk5.png"
			弹孔5.宽度 = 公用模块.取相对像素(10)
			弹孔5.高度 = 公用模块.取相对像素(10)
			弹孔5.左边 = 弹孔X坐标
			弹孔5.顶边 = 弹孔Y坐标
		分支 6
			弹孔6.可视 = 真
			弹孔6.图像 = "dk6.png"
			弹孔6.宽度 = 公用模块.取相对像素(10)
			弹孔6.高度 = 公用模块.取相对像素(10)
			弹孔6.左边 = 弹孔X坐标
			弹孔6.顶边 = 弹孔Y坐标
		分支 7
			弹孔7.可视 = 真
			弹孔7.图像 = "dk7.png"
			弹孔7.宽度 = 公用模块.取相对像素(10)
			弹孔7.高度 = 公用模块.取相对像素(10)
			弹孔7.左边 = 弹孔X坐标
			弹孔7.顶边 = 弹孔Y坐标
		分支 8
			弹孔8.可视 = 真
			弹孔8.图像 = "dk8.png"
			弹孔8.宽度 = 公用模块.取相对像素(10)
			弹孔8.高度 = 公用模块.取相对像素(10)
			弹孔8.左边 = 弹孔X坐标
			弹孔8.顶边 = 弹孔Y坐标
		分支 9
			弹孔9.可视 = 真
			弹孔9.图像 = "dk9.png"
			弹孔9.宽度 = 公用模块.取相对像素(10)
			弹孔9.高度 = 公用模块.取相对像素(10)
			弹孔9.左边 = 弹孔X坐标
			弹孔9.顶边 = 弹孔Y坐标
		分支 10
			弹孔10.可视 = 真
			弹孔10.图像 = "dk10.png"
			弹孔10.宽度 = 公用模块.取相对像素(10)
			弹孔10.高度 = 公用模块.取相对像素(10)
			弹孔10.左边 = 弹孔X坐标
			弹孔10.顶边 = 弹孔Y坐标
		分支 11
			弹孔11.可视 = 真
			弹孔11.图像 = "dk11.png"
			弹孔11.宽度 = 公用模块.取相对像素(10)
			弹孔11.高度 = 公用模块.取相对像素(10)
			弹孔11.左边 = 弹孔X坐标
			弹孔11.顶边 = 弹孔Y坐标
		分支 12
			弹孔12.可视 = 真
			弹孔12.图像 = "dk12.png"
			弹孔12.宽度 = 公用模块.取相对像素(10)
			弹孔12.高度 = 公用模块.取相对像素(10)
			弹孔12.左边 = 弹孔X坐标
			弹孔12.顶边 = 弹孔Y坐标
		分支 13
			弹孔13.可视 = 真
			弹孔13.图像 = "dk13.png"
			弹孔13.宽度 = 公用模块.取相对像素(10)
			弹孔13.高度 = 公用模块.取相对像素(10)
			弹孔13.左边 = 弹孔X坐标
			弹孔13.顶边 = 弹孔Y坐标
		分支 14
			弹孔14.可视 = 真
			弹孔14.图像 = "dk14.png"
			弹孔14.宽度 = 公用模块.取相对像素(10)
			弹孔14.高度 = 公用模块.取相对像素(10)
			弹孔14.左边 = 弹孔X坐标
			弹孔14.顶边 = 弹孔Y坐标
		分支 15
			弹孔15.可视 = 真
			弹孔15.图像 = "dk15.png"
			弹孔15.宽度 = 公用模块.取相对像素(10)
			弹孔15.高度 = 公用模块.取相对像素(10)
			弹孔15.左边 = 弹孔X坐标
			弹孔15.顶边 = 弹孔Y坐标
		分支 16
			弹孔16.可视 = 真
			弹孔16.图像 = "dk16.png"
			弹孔16.宽度 = 公用模块.取相对像素(10)
			弹孔16.高度 = 公用模块.取相对像素(10)
			弹孔16.左边 = 弹孔X坐标
			弹孔16.顶边 = 弹孔Y坐标
		分支 17
			弹孔17.可视 = 真
			弹孔17.图像 = "dk17.png"
			弹孔17.宽度 = 公用模块.取相对像素(10)
			弹孔17.高度 = 公用模块.取相对像素(10)
			弹孔17.左边 = 弹孔X坐标
			弹孔17.顶边 = 弹孔Y坐标
		分支 18
			弹孔18.可视 = 真
			弹孔18.图像 = "dk18.png"
			弹孔18.宽度 = 公用模块.取相对像素(10)
			弹孔18.高度 = 公用模块.取相对像素(10)
			弹孔18.左边 = 弹孔X坐标
			弹孔18.顶边 = 弹孔Y坐标
		分支 19
			弹孔19.可视 = 真
			弹孔19.图像 = "dk19.png"
			弹孔19.宽度 = 公用模块.取相对像素(10)
			弹孔19.高度 = 公用模块.取相对像素(10)
			弹孔19.左边 = 弹孔X坐标
			弹孔19.顶边 = 弹孔Y坐标
		分支 20
			弹孔20.可视 = 真
			弹孔20.图像 = "dk20.png"
			弹孔20.宽度 = 公用模块.取相对像素(10)
			弹孔20.高度 = 公用模块.取相对像素(10)
			弹孔20.左边 = 弹孔X坐标
			弹孔20.顶边 = 弹孔Y坐标
	结束 判断
	
结束 过程

过程 清理弹孔()
	'弹出提示("清理弹孔")
	弹孔1.可视 = 假
	弹孔2.可视 = 假
	弹孔3.可视 = 假
	弹孔4.可视 = 假
	弹孔5.可视 = 假
	弹孔6.可视 = 假
	弹孔7.可视 = 假
	弹孔8.可视 = 假
	弹孔9.可视 = 假
	弹孔10.可视 = 假
	弹孔11.可视 = 假
	弹孔12.可视 = 假
	弹孔13.可视 = 假
	弹孔14.可视 = 假
	弹孔15.可视 = 假
	弹孔16.可视 = 假
	弹孔17.可视 = 假
	弹孔18.可视 = 假
	弹孔19.可视 = 假
	弹孔20.可视 = 假
结束 过程

变量 播报流程 为 整数型
事件 播放音效时钟.周期事件()
	如果 公用模块.报环类型 = -1 则
		靶环音效播放(播放音效)
	否则
	    靶环音效播放("wav")
	结束 如果
结束 事件

过程 靶环音效播放(文件 为 文本型)
	日志输出处理("播放音效:", 文件)
	
	如果 射击音效结果 = "开" 则
		如果 文件 <> "0.0.mp3" 且 公用模块.播报流程 = 0 则
		    播放音乐("ZB.mp3")
	    结束 如果
	结束 如果

	如果 文件 = "wav"  则
		如果 语音结果 <> "关" 则
			
			如果 公用模块.报环类型 = 0 则
				文件 = 公用模块.靶环环数 & ".wav"
				播放音效时钟.时钟周期 = 0
			结束 如果
			
			如果 公用模块.报环类型 = 1 则
				如果 公用模块.播报流程 = 0 则
					文件 = 公用模块.靶环环数 & ".wav"
					播放音效时钟.时钟周期 = 1000
				否则
				    文件 = 公用模块.英文靶环方向 & ".wav"
					公用模块.报环类型 = -1
					公用模块.播报流程 = 0
					播放音效时钟.时钟周期 = 0
				结束 如果
				公用模块.播报流程 = 公用模块.播报流程 + 1
			结束 如果
			
			如果 公用模块.报环类型 = 2 则
				如果 公用模块.播报流程 = 0 则
					文件 = "miss.wav"
					播放音效时钟.时钟周期 = 1000
				否则
				    文件 = 公用模块.英文靶环方向 & ".wav"
					公用模块.报环类型 = -1
					公用模块.播报流程 = 0
					播放音效时钟.时钟周期 = 0
				结束 如果
				公用模块.播报流程 = 公用模块.播报流程 + 1
			结束 如果
			
			如果 公用模块.报环类型 = 3 则
				文件 = "miss.wav"
			结束 如果
			
		    如果 语音结果 = "男" 则
			    播放音乐("man/"& 文件)
	        否则
	            播放音乐("woman/"& 文件)
		    结束 如果
			
			如果 公用模块.报环类型 > 2 则
				播放音效时钟.时钟周期 = 0
			结束 如果
	    结束 如果
	否则
	    如果 语音结果 <> "关" 则
		    如果 语音结果 = "男" 则
			    播放音乐("man/"& 文件)
	        否则
	            播放音乐("woman/"& 文件)
		    结束 如果
			播放音效时钟.时钟周期 = 0
		    播放音效 = ""
	    结束 如果
	结束 如果
	
结束 过程

'结束程序
事件 关闭按钮.被单击()
	关闭按钮.开启特效(5,100,假)
	播放音乐("news.mp3")
	'函数：信息框2(标题 为 文本型,信息 为 文本型,确定按钮标题 为 文本型,取消按钮标题 为 文本型)为 整数型
	变量 结果 为 整数型
	结果 = 信息框2("提示:","确定要结束程序?!","确定","取消")
	如果 结果 = 0 则
	    WIFI管理1.关闭热点()
	    WIFI管理1.置WIFI状态(真)
		关闭数据库()
		公用模块.HOME屏保关闭过程(真)
		公用模块.HOME返回过程(真)
		结束程序()
	结束 如果
结束 事件

'没有使用按钮组件是因为,按钮组件没有"居中缩放"的显示方式,导致变形
事件 校准按钮.被单击()
	日志输出.清空项目()
	校准按钮.开启特效(5,100,假)
	如果 游戏开始状态 = 假 则
        如果 设置开关 = 0 则
		    设置开关 = 1
			标签2.可视 = 假
			标签3.可视 = 假
	    否则
			标签2.可视 = 真
			标签3.可视 = 真
		    设置开关 = 0
			初始游戏软参数()
	    结束 如果
	否则
	    弹出提示("游戏已经开始,不能进入设置选项!")
	结束 如果
	回放时钟.时钟周期 = 0
	刷新UI全局()
结束 事件

	变量 射击调整背景 为 图片框
	变量 射击调整标题 为 标签
	
	变量 语音选择标题 为 标签
	变量 语音选择男 为 单选框
	变量 语音选择女 为 单选框
	变量 语音选择关 为 单选框
	
	变量 预瞄渲染选择标题 为 标签
	变量 预瞄渲染开 为 单选框
	变量 预瞄渲染关 为 单选框

	变量 弹孔堆叠标题 为 标签
	变量 弹孔堆叠开 为 单选框
	变量 弹孔堆叠关 为 单选框
	
	变量 自动结束标题 为 标签
	变量 自动结束开 为 单选框
	变量 自动结束关 为 单选框
	变量 自动结束水平滑块条标题 为 标签
	变量 自动结束水平滑块条 为 水平滑块条
	
	变量 射击音效标题 为 标签
	变量 射击音效开 为 单选框
	变量 射击音效关 为 单选框
	变量 射击音效水平滑块条标题 为 标签
	变量 射击音效水平滑块条 为 水平滑块条
	变量 射击音效试听按钮 为 按钮
	
	变量 上弹音效标题 为 标签
	变量 上弹音效开 为 单选框
	变量 上弹音效关 为 单选框
	变量 上弹音效水平滑块条标题 为 标签
	变量 上弹音效水平滑块条 为 水平滑块条
	变量 上弹音效试听按钮 为 按钮
	
	变量 靶面选择标题 为 标签
	变量 靶面选择0 为 单选框
	变量 靶面选择1 为 单选框
	变量 靶面选择2 为 单选框
	
	变量 预瞄轨迹线宽标签 为 标签
	变量 预瞄轨迹线宽水平滑块条 为 水平滑块条
	
	变量 射击调整完成按钮 为 按钮
	
事件 射击调整按钮.被单击()
	'点击动画
	射击调整按钮.开启特效(5,100,假)
	语音进入状态 = 假
结束 事件

事件 射击调整按钮.开启特效完毕()
	'背景图
	射击调整背景 = 创建 图片框 位于 面板2
	射击调整背景.图像 = "Tips1.png"
	射击调整背景.左边 = 290
	射击调整背景.顶边 = 170
	射击调整背景.宽度 = 673
	射击调整背景.高度 = 436
	
	'主题标签
	射击调整标题 = 创建 标签 位于 面板2
	射击调整标题.标题 = "配置射击相关参数"
	射击调整标题.左边 = 502
	射击调整标题.顶边 = 190
	射击调整标题.宽度 = 270
	射击调整标题.高度 = 30
	射击调整标题.字体颜色 = 白色
	射击调整标题.字体大小 = 12
	射击调整标题.对齐方式 = 靠中居中
	
	'语音选择标题
	语音选择标题 = 创建 标签 位于 面板2
	语音选择标题.标题 = "语音选择:"
	语音选择标题.左边 = 326
	语音选择标题.顶边 = 281
	语音选择标题.宽度 = 87
	语音选择标题.高度 = 22
	语音选择标题.字体颜色 = 白色
	语音选择标题.字体大小 = 8
	语音选择标题.对齐方式 = 靠中居中
	'男
	语音选择男 = 创建 单选框 位于 面板2
	语音选择男.标题 = "男"
	语音选择男.左边 = 422
	语音选择男.顶边 = 278
	语音选择男.宽度 = 52
	语音选择男.高度 = 33
	语音选择男.字体颜色 = 白色
	语音选择男.字体大小 = 7
	'女
	语音选择女 = 创建 单选框 位于 面板2
	语音选择女.标题 = "女"
	语音选择女.左边 = 484
	语音选择女.顶边 = 278
	语音选择女.宽度 = 52
	语音选择女.高度 = 33
	语音选择女.字体颜色 = 白色
	语音选择女.字体大小 = 7
	'关
	语音选择关 = 创建 单选框 位于 面板2
	语音选择关.标题 = "关"
	语音选择关.左边 = 546
	语音选择关.顶边 = 278
	语音选择关.宽度 = 52
	语音选择关.高度 = 33
	语音选择关.字体颜色 = 白色
	语音选择关.字体大小 = 7
	'赋值结果
	判断 语音结果
		分支 "男"
			语音选择男.选中 = 真
		分支 "女"
			语音选择女.选中 = 真
		分支 "关"
			语音选择关.选中 = 真
	结束 判断
	语音进入状态 = 真
	
	'预瞄渲染开关
	预瞄渲染选择标题 = 创建 标签 位于 面板2
	预瞄渲染选择标题.标题 = "预瞄渲染:"
	预瞄渲染选择标题.左边 = 603
	预瞄渲染选择标题.顶边 = 281
	预瞄渲染选择标题.宽度 = 87
	预瞄渲染选择标题.高度 = 22
	预瞄渲染选择标题.字体颜色 = 白色
	预瞄渲染选择标题.字体大小 = 8
	预瞄渲染选择标题.对齐方式 = 靠中居中
	'开
	预瞄渲染开 = 创建 单选框 位于 面板2
	预瞄渲染开.标题 = "开"
	预瞄渲染开.左边 = 696
	预瞄渲染开.顶边 = 278
	预瞄渲染开.宽度 = 52
	预瞄渲染开.高度 = 33
	预瞄渲染开.字体颜色 = 白色
	预瞄渲染开.字体大小 = 7
	'关
	预瞄渲染关 = 创建 单选框 位于 面板2
	预瞄渲染关.标题 = "关"
	预瞄渲染关.左边 = 758
	预瞄渲染关.顶边 = 278
	预瞄渲染关.宽度 = 52
	预瞄渲染关.高度 = 33
	预瞄渲染关.字体颜色 = 白色
	预瞄渲染关.字体大小 = 7
	'赋值结果
	判断 公用模块.预瞄渲染结果
		分支 "开"
			预瞄渲染开.选中 = 真
		分支 "关"
			预瞄渲染关.选中 = 真
	结束 判断
	
	'弹孔堆叠标题
	弹孔堆叠标题 = 创建 标签 位于 面板2
	弹孔堆叠标题.标题 = "弹孔堆叠:"
	弹孔堆叠标题.左边 = 326
	弹孔堆叠标题.顶边 = 317
	弹孔堆叠标题.宽度 = 87
	弹孔堆叠标题.高度 = 22
	弹孔堆叠标题.字体颜色 = 白色
	弹孔堆叠标题.字体大小 = 8
	弹孔堆叠标题.对齐方式 = 靠中居中
	'开
	弹孔堆叠开 = 创建 单选框 位于 面板2
	弹孔堆叠开.标题 = "开"
	弹孔堆叠开.左边 = 422
	弹孔堆叠开.顶边 = 314
	弹孔堆叠开.宽度 = 52
	弹孔堆叠开.高度 = 33
	弹孔堆叠开.字体颜色 = 白色
	弹孔堆叠开.字体大小 = 7
	'关
	弹孔堆叠关 = 创建 单选框 位于 面板2
	弹孔堆叠关.标题 = "关"
	弹孔堆叠关.左边 = 484
	弹孔堆叠关.顶边 = 314
	弹孔堆叠关.宽度 = 52
	弹孔堆叠关.高度 = 33
	弹孔堆叠关.字体颜色 = 白色
	弹孔堆叠关.字体大小 = 7
	'赋值结果
	判断 弹孔叠加结果
		分支 "开"
			弹孔堆叠开.选中 = 真
		分支 "关"
			弹孔堆叠关.选中 = 真
	结束 判断
	
	'设置预瞄轨迹线宽
	预瞄轨迹线宽标签 = 创建 标签 位于 面板2
	预瞄轨迹线宽标签.标题 = "渲染线宽:"
	预瞄轨迹线宽标签.左边 = 570
	预瞄轨迹线宽标签.顶边 = 317
	预瞄轨迹线宽标签.宽度 = 87
	预瞄轨迹线宽标签.高度 = 22
	预瞄轨迹线宽标签.字体颜色 = 白色
	预瞄轨迹线宽标签.字体大小 = 8
	预瞄轨迹线宽标签.对齐方式 = 靠中居中
	
	预瞄轨迹线宽水平滑块条 = 创建 水平滑块条 位于 面板2
	预瞄轨迹线宽水平滑块条.左边 = 649
	预瞄轨迹线宽水平滑块条.顶边 = 313
	预瞄轨迹线宽水平滑块条.宽度 = 268
	预瞄轨迹线宽水平滑块条.高度 = 30
	预瞄轨迹线宽水平滑块条.位置 =  公用模块.渲染宽度
	预瞄轨迹线宽水平滑块条.最大位置 = 5
	
	'自动结束标题
	自动结束标题 = 创建 标签 位于 面板2
	自动结束标题.标题 = "自动结束:"
	自动结束标题.左边 = 326
	自动结束标题.顶边 = 353
	自动结束标题.宽度 = 87
	自动结束标题.高度 = 22
	自动结束标题.字体颜色 = 白色
	自动结束标题.字体大小 = 8
	自动结束标题.对齐方式 = 靠中居中
	'开
	自动结束开 = 创建 单选框 位于 面板2
	自动结束开.标题 = "开"
	自动结束开.左边 = 422
	自动结束开.顶边 = 350
	自动结束开.宽度 = 52
	自动结束开.高度 = 33
	自动结束开.字体颜色 = 白色
	自动结束开.字体大小 = 7
	'关
	自动结束关 = 创建 单选框 位于 面板2
	自动结束关.标题 = "关"
	自动结束关.左边 = 484
	自动结束关.顶边 = 350
	自动结束关.宽度 = 52
	自动结束关.高度 = 33
	自动结束关.字体颜色 = 白色
	自动结束关.字体大小 = 7
	'水平滑块条标题
	自动结束水平滑块条标题 = 创建 标签 位于 面板2
	自动结束水平滑块条标题.标题 = "00"
	自动结束水平滑块条标题.左边 = 535
	自动结束水平滑块条标题.顶边 = 350
	自动结束水平滑块条标题.宽度 = 60
	自动结束水平滑块条标题.高度 = 30
	自动结束水平滑块条标题.字体颜色 = 白色
	自动结束水平滑块条标题.字体大小 = 7
	自动结束水平滑块条标题.对齐方式 = 靠中居中
	'滑块条
	自动结束水平滑块条 = 创建 水平滑块条 位于 面板2
	自动结束水平滑块条.左边 = 567
	自动结束水平滑块条.顶边 = 350
	自动结束水平滑块条.宽度 = 350
	自动结束水平滑块条.高度 = 30
	自动结束水平滑块条.位置 =  自动结束值结果
	自动结束水平滑块条.最大位置 = 20
	'赋值结果
	判断 自动结束结果
		分支 "开"
			自动结束开.选中 = 真
		分支 "关"
			自动结束关.选中 = 真
	结束 判断
	
	'射击音效标题
	射击音效标题 = 创建 标签 位于 面板2
	射击音效标题.标题 = "射击音效:"
	射击音效标题.左边 = 326
	射击音效标题.顶边 = 389
	射击音效标题.宽度 = 87
	射击音效标题.高度 = 22
	射击音效标题.字体颜色 = 白色
	射击音效标题.字体大小 = 8
	射击音效标题.对齐方式 = 靠中居中
	'开
	射击音效开 = 创建 单选框 位于 面板2
	射击音效开.标题 = "开"
	射击音效开.左边 = 422
	射击音效开.顶边 = 386
	射击音效开.宽度 = 52
	射击音效开.高度 = 33
	射击音效开.字体颜色 = 白色
	射击音效开.字体大小 = 7
	'关
	射击音效关 = 创建 单选框 位于 面板2
	射击音效关.标题 = "关"
	射击音效关.左边 = 484
	射击音效关.顶边 = 386
	射击音效关.宽度 = 52
	射击音效关.高度 = 33
	射击音效关.字体颜色 = 白色
	射击音效关.字体大小 = 7
	'赋值结果
	判断 射击音效结果
		分支 "开"
			射击音效开.选中 = 真
		分支 "关"
			射击音效关.选中 = 真
	结束 判断
	'射击音效水平滑块条标题
	射击音效水平滑块条标题 = 创建 标签 位于 面板2
	射击音效水平滑块条标题.标题 = "00"
	射击音效水平滑块条标题.左边 = 535
	射击音效水平滑块条标题.顶边 = 387
	射击音效水平滑块条标题.宽度 = 60
	射击音效水平滑块条标题.高度 = 30
	射击音效水平滑块条标题.字体颜色 = 白色
	射击音效水平滑块条标题.字体大小 = 7
	射击音效水平滑块条标题.对齐方式 = 靠中居中
	'射击音效滑块条
	射击音效水平滑块条 = 创建 水平滑块条 位于 面板2
	射击音效水平滑块条.左边 = 567
	射击音效水平滑块条.顶边 = 387
	射击音效水平滑块条.宽度 = 260
	射击音效水平滑块条.高度 = 30
	射击音效水平滑块条.位置 = 射击音效值结果
	射击音效水平滑块条.最大位置 = 13
	'射击音效试听按钮
	射击音效试听按钮 = 创建 按钮 位于 面板2
    射击音效试听按钮.左边 = 830
    射击音效试听按钮.顶边 = 387
    射击音效试听按钮.宽度 = 75
    射击音效试听按钮.高度 = 30
    射击音效试听按钮.标题 = "试听"
    射击音效试听按钮.字体颜色 = 白色
	射击音效试听按钮.字体大小 = 6
	射击音效试听按钮.对齐方式 = 靠中居中
	
	'上弹音效标题
	上弹音效标题 = 创建 标签 位于 面板2
	上弹音效标题.标题 = "上弹音效:"
	上弹音效标题.左边 = 326
	上弹音效标题.顶边 = 423
	上弹音效标题.宽度 = 87
	上弹音效标题.高度 = 22
	上弹音效标题.字体颜色 = 白色
	上弹音效标题.字体大小 = 8
	上弹音效标题.对齐方式 = 靠中居中
	'开
	上弹音效开 = 创建 单选框 位于 面板2
	上弹音效开.标题 = "开"
	上弹音效开.左边 = 422
	上弹音效开.顶边 = 420
	上弹音效开.宽度 = 52
	上弹音效开.高度 = 33
	上弹音效开.字体颜色 = 白色
	上弹音效开.字体大小 = 7
	'关
	上弹音效关 = 创建 单选框 位于 面板2
	上弹音效关.标题 = "关"
	上弹音效关.左边 = 484
	上弹音效关.顶边 = 420
	上弹音效关.宽度 = 52
	上弹音效关.高度 = 33
	上弹音效关.字体颜色 = 白色
	上弹音效关.字体大小 = 7
	'赋值结果
	判断 上弹音效结果
		分支 "开"
			上弹音效开.选中 = 真
		分支 "关"
			上弹音效关.选中 = 真
	结束 判断
	'上弹音效水平滑块条标题
	上弹音效水平滑块条标题 = 创建 标签 位于 面板2
	上弹音效水平滑块条标题.标题 = "00"
	上弹音效水平滑块条标题.左边 = 535
	上弹音效水平滑块条标题.顶边 = 420
	上弹音效水平滑块条标题.宽度 = 60
	上弹音效水平滑块条标题.高度 = 30
	上弹音效水平滑块条标题.字体颜色 = 白色
	上弹音效水平滑块条标题.字体大小 = 7
	上弹音效水平滑块条标题.对齐方式 = 靠中居中
	'上弹音效滑块条
	上弹音效水平滑块条 = 创建 水平滑块条 位于 面板2
	上弹音效水平滑块条.左边 = 567
	上弹音效水平滑块条.顶边 = 420
	上弹音效水平滑块条.宽度 = 260
	上弹音效水平滑块条.高度 = 30
	上弹音效水平滑块条.位置 = 上弹音效值结果
	上弹音效水平滑块条.最大位置 = 4
	'上弹音效试听按钮
	上弹音效试听按钮 = 创建 按钮 位于 面板2
    上弹音效试听按钮.左边 = 830
    上弹音效试听按钮.顶边 = 420
    上弹音效试听按钮.宽度 = 75
    上弹音效试听按钮.高度 = 30
    上弹音效试听按钮.标题 = "试听"
    上弹音效试听按钮.字体颜色 = 白色
	上弹音效试听按钮.字体大小 = 6
	上弹音效试听按钮.对齐方式 = 靠中居中
	
	'靶面选择
	'变量 靶面选择标题 为 标签
	'变量 靶面选择0 为 单选框
	'变量 靶面选择1 为 单选框
	
	靶面选择标题 = 创建 标签 位于 面板2
	靶面选择标题.标题 = "选择靶面:"
	靶面选择标题.左边 = 326
	靶面选择标题.顶边 = 456
	靶面选择标题.宽度 = 87
	靶面选择标题.高度 = 22
	靶面选择标题.字体颜色 = 白色
	靶面选择标题.字体大小 = 8
	靶面选择标题.对齐方式 = 靠中居中
	
	'0
	靶面选择0 = 创建 单选框 位于 面板2
	靶面选择0.标题 = "靶1"
	靶面选择0.左边 = 422
	靶面选择0.顶边 = 456
	靶面选择0.宽度 = 60
	靶面选择0.高度 = 33
	靶面选择0.字体颜色 = 白色
	靶面选择0.字体大小 = 7
	'1
	靶面选择1 = 创建 单选框 位于 面板2
	靶面选择1.标题 = "靶2"
	靶面选择1.左边 = 484
	靶面选择1.顶边 = 456
	靶面选择1.宽度 = 60
	靶面选择1.高度 = 33
	靶面选择1.字体颜色 = 白色
	靶面选择1.字体大小 = 7
	'2
	靶面选择2 = 创建 单选框 位于 面板2
	靶面选择2.标题 = "靶3"
	靶面选择2.左边 = 546
	靶面选择2.顶边 = 456
	靶面选择2.宽度 = 60
	靶面选择2.高度 = 33
	靶面选择2.字体颜色 = 白色
	靶面选择2.字体大小 = 7
	'赋值结果
	判断 靶面编号
		分支 "0"
			靶面选择0.选中 = 真
		分支 "1"
			靶面选择1.选中 = 真
		分支 "2"
			靶面选择2.选中 = 真
	结束 判断
	
	'完成按钮
	射击调整完成按钮 = 创建 按钮 位于 面板2
    射击调整完成按钮.左边 = 545
    射击调整完成按钮.顶边 = 508
    射击调整完成按钮.宽度 = 192
    射击调整完成按钮.高度 = 49
    射击调整完成按钮.标题 = "完成"
    射击调整完成按钮.字体颜色 = 白色
	射击调整完成按钮.字体大小 = 12
	射击调整完成按钮.对齐方式 = 靠中居中
	
	'弹出面板
	面板2.开启特效(5,300,真)
	高级对话框1.弹出对话框()
结束 事件

事件 预瞄渲染开.选择改变()
    如果 靶面编号 = "0" 则
		弹出提示("\"靶1\"不支持预瞄渲染")
		预瞄渲染开.选中 = 假
		退出
	结束 如果
	如果 预瞄渲染开.选中 = 真 则
		预瞄渲染开.选中 = 真
		预瞄渲染关.选中 = 假
		公用模块.预瞄渲染结果 = "开"
		修改数据库("config", "previewRender", "value",公用模块.预瞄渲染结果)
		查询预瞄渲染结果()
		处理预瞄UI()
	结束 如果
结束 事件

事件 预瞄渲染关.选择改变()
	如果 预瞄渲染关.选中 = 真 则
		预瞄渲染开.选中 = 假
		预瞄渲染关.选中 = 真
		公用模块.预瞄渲染结果 = "关"
		修改数据库("config", "previewRender", "value",公用模块.预瞄渲染结果)
		查询预瞄渲染结果()
		画板1.清空()
		处理预瞄UI()
	结束 如果
结束 事件

事件 预瞄轨迹线宽水平滑块条.位置被改变(位置 为 整数型)
	如果 位置 <= 1 则
		位置 = 1
	结束 如果
	预瞄轨迹线宽水平滑块条.位置 = 位置
	修改数据库("config", "renderWidth", "value",位置)
	查询渲染宽度结果()
结束 事件

事件 自动结束水平滑块条.位置被改变(位置 为 整数型)
	如果 位置 <= 1 则
		位置 = 1
	结束 如果
	自动结束水平滑块条.位置 = 位置
	自动结束水平滑块条标题.内容 = 位置
	修改数据库("config", "autoEndValue", "value",位置)
	查询自动结束结果()
结束 事件

事件 射击音效水平滑块条.位置被改变(位置 为 整数型)
	如果 位置 <= 1 则
		位置 = 1
	结束 如果
	射击音效水平滑块条.位置 = 位置
	射击音效水平滑块条标题.内容 = 位置
	修改数据库("config", "shootingSoundValue", "value",位置)
	查询射击音效结果()
结束 事件
事件 射击音效试听按钮.被单击()
	播放音乐("gun/shooting"& 射击音效值结果 &".mp3")
结束 事件

事件 上弹音效水平滑块条.位置被改变(位置 为 整数型)
	如果 位置 <= 1 则
		位置 = 1
	结束 如果
	上弹音效水平滑块条.位置 = 位置
	上弹音效水平滑块条标题.内容 = 位置
    修改数据库("config", "topPlaySoundValue", "value",位置)
	查询上弹音效结果()
结束 事件

事件 上弹音效试听按钮.被单击()
	播放音乐("gun/goOn"& 上弹音效值结果 &".mp3")
结束 事件

事件 射击调整完成按钮.被单击()
	高级对话框1.关闭对话框()
结束 事件

事件 面板设置加.被单击()
	面板宽度 = 面板宽度 + 1
 	面板高度 = 面板高度 + 1	
	修改面板参数((面板宽度 + 面板高度) / 2)
结束 事件

事件 面板设置减.被单击()
    面板宽度 = 面板宽度 - 1
 	面板高度 = 面板高度 - 1
	修改面板参数((面板宽度 + 面板高度) / 2)
结束 事件

过程 修改面板参数(参数 为 整数型)
	面板参数 = 参数
	修改数据库("config", "panel", "value",参数)
	刷新UI全局()
结束 过程
	
事件 靶环中心点X减.被单击()
	靶环中心点X = 靶环中心点X - 1
	靶环中心点X修改(靶环中心点X)
结束 事件

事件 靶环中心点X加.被单击()
	靶环中心点X = 靶环中心点X + 1
	靶环中心点X修改(靶环中心点X)
结束 事件

事件 靶环中心点Y减.被单击()	
	靶环中心点Y = 靶环中心点Y - 1
	靶环中心点Y修改(靶环中心点Y)
结束 事件

事件 靶环中心点Y加.被单击()
	靶环中心点Y = 靶环中心点Y + 1
	靶环中心点Y修改(靶环中心点Y)
结束 事件

过程 靶环中心点X修改(参数 为 整数型)
	修改数据库("config", "coreX", "value",参数)
	刷新UI全局()
结束 过程
过程 靶环中心点Y修改(参数 为 整数型)
	修改数据库("config", "coreY", "value",参数)
	刷新UI全局()
结束 过程
	
事件 Y范围上减.被单击()
	本机Y最大值 = 本机Y最大值 + 1
	范围Y上修改(本机Y最大值)
结束 事件

事件 Y范围上加.被单击()
	本机Y最大值 = 本机Y最大值 - 1
	范围Y上修改(本机Y最大值)
结束 事件

事件 Y范围下减.被单击()
	本机Y最小值 = 本机Y最小值 - 1
	范围Y下修改(本机Y最小值)
结束 事件

事件 Y范围下加.被单击()
	本机Y最小值 = 本机Y最小值 + 1
	范围Y下修改(本机Y最小值)
结束 事件	

事件 X范围左减.被单击()
	本机X最小值 = 本机X最小值 - 1
	范围X左修改(本机X最小值)
结束 事件

事件 X范围左加.被单击()
	本机X最小值 = 本机X最小值 + 1
	范围X左修改(本机X最小值)
结束 事件

事件 X范围右减.被单击()
	本机X最大值 = 本机X最大值 - 1
	范围X右修改(本机X最大值)
结束 事件

事件 X范围右加.被单击()
	本机X最大值 = 本机X最大值 + 1
	范围X右修改(本机X最大值)
结束 事件

过程 范围Y上修改(参数 为 整数型)
	修改数据库("config", "upEdgeY", "value",参数)
	刷新UI全局()
结束 过程
过程 范围Y下修改(参数 为 整数型)
	修改数据库("config", "dowEdgeY", "value",参数)
	刷新UI全局()
结束 过程
过程 范围X左修改(参数 为 整数型)
	修改数据库("config", "leftEdgeX", "value",参数)
	刷新UI全局()
结束 过程
过程 范围X右修改(参数 为 整数型)
	修改数据库("config", "rightEdgeX", "value",参数)
	刷新UI全局()
结束 过程
	
事件 弹孔偏移X减.被单击()
    公用模块.弹孔偏移X = 公用模块.弹孔偏移X - 1
	弹孔偏移X修改(公用模块.弹孔偏移X)
结束 事件
	
事件 弹孔偏移X加.被单击()
    公用模块.弹孔偏移X = 公用模块.弹孔偏移X + 1
	弹孔偏移X修改(公用模块.弹孔偏移X)
结束 事件

事件 弹孔偏移Y减.被单击()
	公用模块.弹孔偏移Y = 公用模块.弹孔偏移Y - 1
	弹孔偏移Y修改(公用模块.弹孔偏移Y)
结束 事件

事件 弹孔偏移Y加.被单击()
	公用模块.弹孔偏移Y = 公用模块.弹孔偏移Y + 1
	弹孔偏移Y修改(公用模块.弹孔偏移Y)
结束 事件
	
过程 弹孔偏移X修改(参数 为 整数型)
	修改数据库("config", "bulletOffsetX", "value",参数)
	刷新UI全局()
结束 过程

过程 弹孔偏移Y修改(参数 为 整数型)
	修改数据库("config", "bulletOffsetY", "value",参数)
	刷新UI全局()
结束 过程

事件 清理弹孔按钮.被单击()
	清理弹孔按钮.开启特效(5,100,假)
	'画板1.开启特效(3,800,假)
	清理弹孔()
	渲染面板()
结束 事件

事件 主窗口.按下某键(键代码 为 整数型,传址 屏蔽 为 逻辑型)
	恢复屏幕亮度()
	弹出提示(键代码)
	'退出调整模式
	如果 键代码 = 131 则
        UI调整状态 = 0
	结束 如果
	
	'进入调整高级表格位置
	如果 键代码 = 132 则
        UI调整状态 = 1
	结束 如果
	
	'进入调整高级表格位置
	如果 键代码 = 133 则
        UI调整状态 = 2
	结束 如果
	'进入调整高级表格大小
	如果 键代码 = 134 则
        UI调整状态 = 3
	结束 如果
	
	'进入调整高级表格大小
	如果 键代码 = 135 则
        UI调整状态 = 4
	结束 如果
	
	'使用方向键调整表格
	如果 UI调整状态 = 1 则
		如果 键代码 = 19 则
            统计表格顶边 = 统计表格顶边 - 1
		结束 如果
		如果 键代码 = 20 则
            统计表格顶边 = 统计表格顶边 + 1
	    结束 如果
		如果 键代码 = 21 则
            统计表格左边 = 统计表格左边 - 1
		结束 如果
		如果 键代码 = 22 则
            统计表格左边 = 统计表格左边 + 1
		结束 如果
	结束 如果
	
	'调整表格宽高
	如果 UI调整状态 = 2 则
		如果 键代码 = 19 则
            统计表格高 = 统计表格高 - 1
		结束 如果
		如果 键代码 = 20 则
            统计表格高 = 统计表格高 + 1
		结束 如果
		如果 键代码 = 21 则
            统计表格宽 = 统计表格宽 - 1
	    结束 如果
	    如果 键代码 = 22 则
            统计表格宽 = 统计表格宽 + 1
	    结束 如果
	结束 如果
	
	'使用方向键调整表格
	如果 UI调整状态 = 3 则
		如果 键代码 = 19 则
            高级列表顶边 = 高级列表顶边 - 1
		结束 如果
		如果 键代码 = 20 则
            高级列表顶边 = 高级列表顶边 + 1
	    结束 如果
		如果 键代码 = 21 则
            高级列表左边 = 高级列表左边 - 1
		结束 如果
		如果 键代码 = 22 则
            高级列表左边 = 高级列表左边 + 1
		结束 如果
	结束 如果
	
	'调整列表框高
	如果 UI调整状态 = 4 则
		如果 键代码 = 19 则
            高级列表高 = 高级列表高 - 1
		结束 如果
		如果 键代码 = 20 则
            高级列表高 = 高级列表高 + 1
		结束 如果
		如果 键代码 = 21 则
            高级列表宽 = 高级列表宽 - 1
	    结束 如果
	    如果 键代码 = 22 则
            高级列表宽 = 高级列表宽 + 1
	    结束 如果
	结束 如果
		
结束 事件

事件 开始游戏按钮.被单击()
	如果 游戏开始状态 = 假 则
		公用模块.开始的结束 = 假
		开始游戏按钮.标题 = "结束"
		游戏开始()
	否则
	   开始游戏按钮.标题 = "开始"
	   游戏结束()
	结束 如果
结束 事件

过程 游戏开始()
	初始游戏软参数()
	播放音乐("countdown.mp3")
	射击间隔时钟.时钟周期 = 100
	开始游戏按钮.可用 = 假
	射击总计时 = 0.0
	射击总计分 = 0.0
	射击总计秒 = 0.0
	射击总计秒十 = 0.0
结束 过程

过程 游戏结束()
	游戏开始状态 = 假
	播放音乐("gameend.mp3")
	初始游戏软参数()
	开始计时 = 0
	射击间隔时钟.时钟周期 = 0
结束 过程

事件 获得校准数据按钮.被单击()
	校准数据时钟.时钟周期 = 1000
	公用模块.校准等待延迟 = 0
结束 事件

事件 校准数据时钟.周期事件()
	
	公用模块.校准等待延迟 = 公用模块.校准等待延迟 + 1
	
	如果 公用模块.校准等待延迟 = 1 则
		'弹出提示("请求靶面校准数据,请稍后....")
	结束 如果
	
	如果 公用模块.校准等待延迟 = 2 则
		变量 校准数据请求指令 为 文本型
		校准数据请求指令 = "@apply:{\"type\":\"calibration\"};\r\n"
		服务器1.发送数据(文本到字节(校准数据请求指令,"UTF-8"),客户端001地址,客户端001端口)
	结束 如果
结束 事件

事件 初始化设置按钮.被单击()
	播放音乐("news.mp3")
	变量 结果 为 整数型
	结果 = 信息框2("提示:","确定恢复初始设置完成后,软件将自动关闭,从新启动即可!","确定","取消")
	如果 结果 = 0 则
		删除数据表("config")
		结束程序()
	结束 如果
结束 事件

事件 高级对话框1.对话框被关闭()
	服务器2.关闭服务器()
	恢复屏幕亮度()
	面板2.清空组件()
	面板2.可视 = 假
结束 事件

	变量 帮助状态 为 逻辑型
	变量 帮助载入完成 为 逻辑型
事件 帮助按钮.被单击()
	如果 帮助状态 = 假 则
		帮助状态 = 真
		
		如果 设置开关 = 1 则
			设置开关 = 0
			刷新UI全局()
		结束 如果
	
		帮助按钮.标题 = "关闭帮助"
		浏览框1.到顶层()
		浏览框1.可视  = 真
		浏览框1.启用缩放 = 假
		浏览框1.启用缓存 = 假
		如果 帮助载入完成 = 假 则
			浏览框1.清除APP缓存()
			浏览框1.释放内存()
			浏览框1.跳转("https://doc.lnzhiyuan.cn:82/doc/web/#/19/58")
			进度圈1.可视 = 真
		结束 如果
	否则
	    帮助状态 = 假
		浏览框1.可视  = 假
		帮助按钮.标题 = "开启帮助"
	结束 如果
结束 事件

事件 浏览框1.载入完毕(地址 为 文本型)
	帮助载入完成 = 真
	进度圈1.可视 = 假
结束 事件

事件 回放上一个.被单击()
	如果 公用模块.回放指针 <= 0 则
		弹出提示("没有回放记录!")
		退出
	结束 如果
	
	公用模块.回放指针 = 公用模块.回放指针 - 1
	
	如果 公用模块.回放指针 < 1 则
		弹出提示("没有更多记录!")
		公用模块.回放指针 = 1
	结束 如果
	公用模块.回放计次 = 0
	回放标记标签.标题 = "回放记录:" & 公用模块.回放指针
	调整回放时钟周期()
结束 事件

事件 回放下一个按钮.被单击()
	如果 公用模块.回放指针 >= 公用模块.射击计数 则
		弹出提示("没有更多的记录!")
		退出
	结束 如果
	
	公用模块.回放指针 = 公用模块.回放指针 + 1
	
	如果 公用模块.回放指针 > 20 则
		弹出提示("没有更多记录!")
		公用模块.回放指针 = 20
	结束 如果
	公用模块.回放计次 = 0
	回放标记标签.标题 = "回放记录:" & 公用模块.回放指针
	调整回放时钟周期()
结束 事件

事件 分享按钮.被单击()
	屏幕截图(取存储卡路径() & "/HDDZ/share.png")
	分享按钮.开启特效(5,100,假)
结束 事件

事件 切换按钮.被单击()
	切换按钮.开启特效(5,100,假)
	如果 公用模块.预瞄渲染结果 = "关" 则
		公用模块.预瞄渲染结果 = "开"
	否则
	    公用模块.预瞄渲染结果 = "关"
	结束 如果
	修改数据库("config", "previewRender", "value",公用模块.预瞄渲染结果)
	查询预瞄渲染结果()
	刷新UI全局()
结束 事件

    变量 分享背景 为 图片框
	变量 分享标题 为 标签
	变量 分享描述 为 标签
	变量 分享二维码 为 图片框
	变量 访问二维码 为 字节型()
	变量 分享退出按钮 为 按钮
事件 分享按钮.开启特效完毕()
	服务器2.启动服务器(8080)
	
	变量 局域网地址 为 文本型
	局域网地址 = "http://" & WIFI管理1.取内网IP() & ":8080/"
	
	如果 局域网地址 = "http://:8080/" 则
		局域网地址 = "http://************:8080/"
	结束 如果
	
	'弹出提示(局域网地址)
	访问二维码 = 二维码1.生成二维码(局域网地址,500,500)
	
    '背景图
	分享背景 = 创建 图片框 位于 面板2
	分享背景.图像 = "Tips1.png"
	分享背景.左边 = 290
	分享背景.顶边 = 170
	分享背景.宽度 = 673
	分享背景.高度 = 436
	'主题标签
	分享标题 = 创建 标签 位于 面板2
	分享标题.标题 = "分享成绩"
	分享标题.左边 = 502
	分享标题.顶边 = 190
	分享标题.宽度 = 270
	分享标题.高度 = 30
	分享标题.字体颜色 = 白色
	分享标题.字体大小 = 12
	分享标题.对齐方式 = 靠中居中
	
	'分享描述
	分享描述 = 创建 标签 位于 面板2
	分享描述.标题 = "连接至WIFI   ：HDDZ2G1" & "\n"_
	& "密码(纯数字)：88888888\n"_
	& "连接成功后，使用相机或浏览器扫描识别，左侧二维码即得到成绩图像。\n"_
	& "保存图像后，可发送好友或朋友圈！"_
	& "(注：本功能在游戏结束后有效！)"

	分享描述.左边 = 630
	分享描述.顶边 = 180
	分享描述.宽度 = 300
	分享描述.高度 = 300
	分享描述.字体颜色 = 白色
	分享描述.字体大小 = 9
	分享描述.对齐方式 = 靠中左对齐
	
	'分享二维码
	分享二维码 = 创建 图片框 位于 面板2
	分享二维码.左边 = 320
	分享二维码.顶边 = 260
	分享二维码.宽度 = 300
	分享二维码.高度 = 300
	分享二维码.背景颜色 = 白色
	分享二维码.载入字节图片(访问二维码)
	
	'分享退出按钮
	分享退出按钮 = 创建 按钮 位于 面板2
    分享退出按钮.左边 = 680
    分享退出按钮.顶边 = 508
    分享退出按钮.宽度 = 192
    分享退出按钮.高度 = 49
    分享退出按钮.标题 = "退出分享"
    分享退出按钮.字体颜色 = 白色
	分享退出按钮.字体大小 = 12
	分享退出按钮.对齐方式 = 靠中居中

	'弹出面板
	面板2.开启特效(5,300,真)
	高级对话框1.弹出对话框()	
结束 事件

事件 分享退出按钮.被单击()
	高级对话框1.关闭对话框()
结束 事件

变量 响应头 为 文本型
过程 处理相应头()
	响应头 =  "HTTP/1.1 200 OK\r\n" _
	        & "Date: "& 时间戳到时间文本(取现行时间戳(1)) &"\r\n" _
            & "Server: HDDZWEBSERVER\r\n" _
			& "Content-Type: image/png;charset=UTF-8\r\n" _
			& "Cache-Control: no-cache\r\n" _
			& "Connection: keep-alive\r\n" _
			& "Content-Length:" _
			& "Refresh: 60\r\n"_
			& "\r\n"
结束 过程

变量 访问数据 为 字节型()
事件 服务器2.收到数据(数据 为 字节型(),IP地址 为 文本型,端口 为 整数型)
	访问数据 = 读入字节文件(取存储卡路径() & "/HDDZ/share.png")
	处理相应头()
	响应头 = 子文本替换(响应头,"Content-Length:","Content-Length:" & 取字节集长度(访问数据) & "\r\n")
	服务器2.发送数据(文本到字节(响应头,"UTF-8"),IP地址,端口)
	服务器2.发送数据(访问数据,IP地址,端口)
	服务器2.发送数据(文本到字节("\r\n0\r\n\r\n","UTF-8"),IP地址,端口)
结束 事件

事件 回放加速按钮.被单击()
	回放时钟.时钟周期 = 0
	公用模块.渲染速度 = 公用模块.渲染速度 + 1
	如果 公用模块.渲染速度 > 5 则
		公用模块.渲染速度 = 5
	结束 如果
	修改数据库("config", "renderSpeed", "value",公用模块.渲染速度)
	查询预染速度结果()
	调整回放时钟周期()
结束 事件

事件 回放减速按钮.被单击()
	回放时钟.时钟周期 = 0
	公用模块.渲染速度 = 公用模块.渲染速度 - 1
	如果 公用模块.渲染速度 < 1 则
		公用模块.渲染速度 = 1
	结束 如果
	修改数据库("config", "renderSpeed", "value",公用模块.渲染速度)
	查询预染速度结果()
	调整回放时钟周期()
结束 事件

过程 调整回放时钟周期()
	判断 公用模块.渲染速度
		分支 1
			公用模块.渲染速度调整 = 256
		分支 2
			公用模块.渲染速度调整 = 128
		分支 3
			公用模块.渲染速度调整 = 64
		分支 4
			公用模块.渲染速度调整 = 32
		分支 5
			公用模块.渲染速度调整 = 16
	结束 判断
	'弹出提示(公用模块.预瞄射击历史数据组[0])
	回放时钟.时钟周期 = 公用模块.渲染速度调整
结束 过程


事件 模拟射击按钮.被单击()
	
	模拟射击过程 = 模拟射击过程 + 1
	
	变量 校准数据JSON对象 为 对象
	校准数据JSON对象 = JSON操作1.解析(视觉靶校准数据)
	变量 物理X中心点 为 文本型
	变量 物理Y中心点 为 文本型	
	变量 物理边界X最小值 为 整数型
	变量 物理边界X最大值 为 整数型
	变量 物理边界Y最小值 为 整数型
	变量 物理边界Y最大值 为 整数型
	'校准数据[Xcenter(中心X),Ycenter(中心Y),XSmall(最小X),XLarge(最大X),YSmall(最小Y),YLarge(最大Y)]
	物理X中心点 = JSON操作1.取文本值(校准数据JSON对象,"Xcenter")
	物理Y中心点 = JSON操作1.取文本值(校准数据JSON对象,"Ycenter")
	物理边界X最小值 = JSON操作1.取文本值(校准数据JSON对象,"XSmall")
	物理边界X最大值 = JSON操作1.取文本值(校准数据JSON对象,"XLarge")
	物理边界Y最小值 = JSON操作1.取文本值(校准数据JSON对象,"YSmall")
	物理边界Y最大值 = JSON操作1.取文本值(校准数据JSON对象,"YLarge")
	'{"Xcenter": 150, "XSmall": 54, "XLarge": 260, "Ycenter": 118, "YSmall": 13, "YLarge": 209}
	
	变量 模拟射击数据 为 文本型
	判断 模拟射击过程
		分支 1
			模拟射击数据 = "@optics:["&物理X中心点&","&物理Y中心点&","&物理X中心点&","&物理Y中心点&","&物理X中心点&","&物理Y中心点&"," & 物理X中心点 & ","& 物理Y中心点 &"];\r\n"
		分支 2
			模拟射击数据 = "@optics:["&物理X中心点&","&物理Y中心点&","&物理X中心点&","&物理Y中心点&","&物理X中心点&","&物理Y中心点&"," & 物理边界X最小值 & ","& 物理Y中心点 &"];\r\n"
		分支 3
			模拟射击数据 = "@optics:["&物理X中心点&","&物理Y中心点&","&物理X中心点&","&物理Y中心点&","&物理X中心点&","&物理Y中心点&"," & 物理边界X最大值 & ","& 物理Y中心点 &"];\r\n"
		分支 4
			模拟射击数据 = "@optics:["&物理X中心点&","&物理Y中心点&","&物理X中心点&","&物理Y中心点&","&物理X中心点&","&物理Y中心点&"," & 物理X中心点 & ","& 物理边界Y最小值 &"];\r\n"
		分支 5
			模拟射击数据 = "@optics:["&物理X中心点&","&物理Y中心点&","&物理X中心点&","&物理Y中心点&","&物理X中心点&","&物理Y中心点&"," & 物理X中心点 & ","& 物理边界Y最大值 &"];\r\n"
			模拟射击过程 = 0
	结束 判断
	
	弹出提示(模拟射击数据)
	
    变量 模拟射击数据字节集 为 字节型()
	模拟射击数据字节集 = 文本到字节(模拟射击数据,"UTF-8")
	接收信息处理(模拟射击数据字节集,"0.0.0.0","8888")
结束 事件

事件 缩放校准X减按钮.被单击()
	如果 模拟射击过程 = 0 则
		弹出提示("没有射击数据")
	否则
		公用模块.放大偏移X量 = 公用模块.放大偏移X量 - 1
		修改数据库("config", "ZoomOffsetX", "value",公用模块.放大偏移X量)
		缩放校准值标签.标题 = "X:" & 公用模块.放大偏移X量 & "," & "Y:" & 公用模块.放大偏移Y量
		预瞄轨迹画板.画图片缩放部分("yxb1280.png",0,0,1280,1280,预瞄转换坐标数组(预瞄射击数据数组成员-2)+公用模块.放大偏移X量,预瞄转换坐标数组(预瞄射击数据数组成员-1)+公用模块.放大偏移Y量,300,300)
	结束 如果
结束 事件

事件 缩放校准X加按钮.被单击()
	如果 模拟射击过程 = 0 则
		弹出提示("没有射击数据")
	否则
	    公用模块.放大偏移X量 = 公用模块.放大偏移X量 + 1
	    修改数据库("config", "ZoomOffsetX", "value",公用模块.放大偏移X量)
	    缩放校准值标签.标题 = "X:" & 公用模块.放大偏移X量 & "," & "Y:" & 公用模块.放大偏移Y量
	    预瞄轨迹画板.画图片缩放部分("yxb1280.png",0,0,1280,1280,预瞄转换坐标数组(预瞄射击数据数组成员-2)+公用模块.放大偏移X量,预瞄转换坐标数组(预瞄射击数据数组成员-1)+公用模块.放大偏移Y量,300,300)
	结束 如果
结束 事件

事件 缩放校准Y减按钮.被单击()
	如果 模拟射击过程 = 0 则
		弹出提示("没有射击数据")
	否则
	    公用模块.放大偏移Y量 = 公用模块.放大偏移Y量 - 1
	    修改数据库("config", "ZoomOffsetY", "value",公用模块.放大偏移Y量)
	    缩放校准值标签.标题 = "X:" & 公用模块.放大偏移X量 & "," & "Y:" & 公用模块.放大偏移Y量
	    预瞄轨迹画板.画图片缩放部分("yxb1280.png",0,0,1280,1280,预瞄转换坐标数组(预瞄射击数据数组成员-2)+公用模块.放大偏移X量,预瞄转换坐标数组(预瞄射击数据数组成员-1)+公用模块.放大偏移Y量,300,300)
	结束 如果
结束 事件

事件 缩放校准Y加按钮.被单击()
	如果 模拟射击过程 = 0 则
		弹出提示("没有射击数据")
	否则
	    公用模块.放大偏移Y量 = 公用模块.放大偏移Y量 + 1
	    修改数据库("config", "ZoomOffsetY", "value",公用模块.放大偏移Y量)
	    缩放校准值标签.标题 = "X:" & 公用模块.放大偏移X量 & "," & "Y:" & 公用模块.放大偏移Y量
	    预瞄轨迹画板.画图片缩放部分("yxb1280.png",0,0,1280,1280,预瞄转换坐标数组(预瞄射击数据数组成员-2)+公用模块.放大偏移X量,预瞄转换坐标数组(预瞄射击数据数组成员-1)+公用模块.放大偏移Y量,300,300)
	结束 如果
结束 事件

事件 关闭靶面按钮.被单击()
	弹出提示("发送远程靶面关机指令,稍后将自动关机......")
	变量 校准数据请求指令 为 文本型
	校准数据请求指令 = "@apply:{\"type\":\"shutDown\"};\r\n"
	服务器1.发送数据(文本到字节(校准数据请求指令,"UTF-8"),客户端001地址,客户端001端口)
结束 事件

事件 WIFI状态按钮.被单击()
	WIFI状态按钮.开启特效(5,100,假)
	应用跳转1.跳转到("com.android.settings","com.android.settings.Settings$WifiTetherSettingsActivity")
结束 事件

事件 账户登录按钮.被单击()
	账户登录按钮.开启特效(5,100,假)
结束 事件

变量 账户登录背景图片框 为 图片框
变量 账户登录主题标签 为 标签
变量 账户登录姓名标签 为 标签
变量 账户登录密码标签 为 标签
变量 账户登录姓名编辑框 为 编辑框
变量 账户登录密码编辑框 为 密码编辑框
变量 账户确认登录按钮 为 按钮

事件 账户登录按钮.开启特效完毕()
	
	创建账户射击管理页面过程()
	退出
	
	'背景图
	账户登录背景图片框 = 创建 图片框 位于 面板2
	账户登录背景图片框.图像 = "Tips1.png"	
	账户登录背景图片框.左边 = 公用模块.取宽度绝对像素(184)
	账户登录背景图片框.顶边 = 公用模块.取高度绝对像素(103)
	账户登录背景图片框.宽度 = 公用模块.取宽度绝对像素(408)
	账户登录背景图片框.高度 = 公用模块.取高度绝对像素(252)
	
	'主题标签
	账户登录主题标签 = 创建 标签 位于 面板2
	账户登录主题标签.左边 = 公用模块.取宽度绝对像素(184)
	账户登录主题标签.顶边 = 公用模块.取高度绝对像素(114)
	账户登录主题标签.宽度 = 公用模块.取宽度绝对像素(408)
	账户登录主题标签.高度 = 公用模块.取高度绝对像素(30)
	账户登录主题标签.字体颜色 = 白色
	账户登录主题标签.字体大小 = 公用模块.取字体绝对大小(15)
	账户登录主题标签.标题 = "账户登录"
	账户登录主题标签.对齐方式 = 靠中居中
	
	'姓名标签
	账户登录姓名标签 = 创建 标签 位于 面板2
	账户登录姓名标签.标题 = "姓名:"
	账户登录姓名标签.左边 = 公用模块.取宽度绝对像素(256)
	账户登录姓名标签.顶边 = 公用模块.取高度绝对像素(184)
	账户登录姓名标签.宽度 = 公用模块.取宽度绝对像素(45)
	账户登录姓名标签.高度 = 公用模块.取高度绝对像素(30)
	账户登录姓名标签.字体颜色 = 白色
	账户登录姓名标签.字体大小 = 公用模块.取字体绝对大小(12)
	账户登录姓名标签.对齐方式 = 靠中居中
	
	'密码标签
	账户登录密码标签 = 创建 标签 位于 面板2
	账户登录密码标签.标题 = "密码:"
	账户登录密码标签.左边 = 公用模块.取宽度绝对像素(256)
	账户登录密码标签.顶边 = 公用模块.取高度绝对像素(231)
	账户登录密码标签.宽度 = 公用模块.取宽度绝对像素(45)
	账户登录密码标签.高度 = 公用模块.取高度绝对像素(30)
	账户登录密码标签.字体颜色 = 白色
	账户登录密码标签.字体大小 = 公用模块.取字体绝对大小(12)
	账户登录密码标签.对齐方式 = 靠中居中
	
	账户登录姓名编辑框 = 创建 编辑框 位于 面板2
	账户登录姓名编辑框.左边 = 公用模块.取宽度绝对像素(312)
	账户登录姓名编辑框.顶边 = 公用模块.取高度绝对像素(184)
	账户登录姓名编辑框.宽度 = 公用模块.取宽度绝对像素(205)
	账户登录姓名编辑框.高度 = 公用模块.取高度绝对像素(30)
	账户登录姓名编辑框.字体大小 = 公用模块.取字体绝对大小(12)
	账户登录姓名编辑框.字体颜色 = 白色
	账户登录姓名编辑框.对齐方式 = 靠中居中
	
	账户登录密码编辑框 = 创建 密码编辑框 位于 面板2
	账户登录密码编辑框.左边 = 公用模块.取宽度绝对像素(312)
	账户登录密码编辑框.顶边 = 公用模块.取高度绝对像素(230)
	账户登录密码编辑框.宽度 = 公用模块.取宽度绝对像素(205)
	账户登录密码编辑框.高度 = 公用模块.取高度绝对像素(30)
	账户登录密码编辑框.字体大小 = 公用模块.取字体绝对大小(12)
	账户登录密码编辑框.字体颜色 = 白色
	账户登录密码编辑框.对齐方式 = 靠中居中
	
	账户登录密码编辑框 = 创建 密码编辑框 位于 面板2
	账户登录密码编辑框.左边 = 公用模块.取宽度绝对像素(312)
	账户登录密码编辑框.顶边 = 公用模块.取高度绝对像素(230)
	账户登录密码编辑框.宽度 = 公用模块.取宽度绝对像素(205)
	账户登录密码编辑框.高度 = 公用模块.取高度绝对像素(30)
	账户登录密码编辑框.字体大小 = 公用模块.取字体绝对大小(12)
	账户登录密码编辑框.字体颜色 = 白色
	账户登录密码编辑框.对齐方式 = 靠中居中
	
	账户确认登录按钮 = 创建 按钮 位于 面板2
	账户确认登录按钮.左边 = 公用模块.取宽度绝对像素(329)
	账户确认登录按钮.顶边 = 公用模块.取高度绝对像素(303)
	账户确认登录按钮.宽度 = 公用模块.取宽度绝对像素(118)
	账户确认登录按钮.高度 = 公用模块.取高度绝对像素(32)
	账户确认登录按钮.字体大小 = 公用模块.取字体绝对大小(12)
	账户确认登录按钮.字体颜色 = 白色
	账户确认登录按钮.对齐方式 = 靠中居中
	账户确认登录按钮.标题  = "确认登录"
	
	'弹出面板
	面板2.开启特效(5,300,真)
	高级对话框1.弹出对话框()
结束 事件


事件 账户确认登录按钮.被单击()
	变量 JSON对象 为 对象
	JSON对象 = JSON操作1.创建对象()
	JSON对象 = JSON操作1.解析(管理员)
	变量 管理账号 为 文本型
	管理账号 = JSON操作1.取文本值(JSON对象,"account")
	变量 管理密码 为 文本型
	管理密码 = JSON操作1.取文本值(JSON对象,"password")
	
	如果 账户登录姓名编辑框.内容 = "admin" 则
		如果 账户登录姓名编辑框.内容 = 管理账号 且 账户登录密码编辑框.内容 = 管理密码 则
			弹出提示("登录成功")
			高级对话框1.关闭对话框()
			创建账户射击管理页面过程()
		否则
			弹出提示("登录失败")
		结束 如果
	否则
		'弹出提示("用户登录")

	结束 如果
结束 事件

    变量 账户管理背景 为 图片框

	变量 账户管理人员管理高级列表框 为 高级列表框
	变量 账户管理添加账户按钮 为 按钮
	变量 账户管理年份后退按钮 为 按钮
	变量 账户管理年份显示标签 为 标签
	变量 账户管理年费前进按钮 为 按钮
	
	变量 账户管理01月按钮 为 按钮
	变量 账户管理02月按钮 为 按钮
	变量 账户管理03月按钮 为 按钮
	变量 账户管理04月按钮 为 按钮
	变量 账户管理05月按钮 为 按钮
	变量 账户管理06月按钮 为 按钮
	变量 账户管理07月按钮 为 按钮
	变量 账户管理08月按钮 为 按钮
	变量 账户管理09月按钮 为 按钮
	变量 账户管理10月按钮 为 按钮
	变量 账户管理11月按钮 为 按钮
	变量 账户管理12月按钮 为 按钮
	
	变量 账户管理01号按钮 为 按钮
	变量 账户管理02号按钮 为 按钮
	变量 账户管理03号按钮 为 按钮
	变量 账户管理04号按钮 为 按钮
	变量 账户管理05号按钮 为 按钮
	变量 账户管理06号按钮 为 按钮
	变量 账户管理07号按钮 为 按钮
	变量 账户管理08号按钮 为 按钮
	变量 账户管理09号按钮 为 按钮
	变量 账户管理10号按钮 为 按钮
	变量 账户管理11号按钮 为 按钮
	变量 账户管理12号按钮 为 按钮
	变量 账户管理13号按钮 为 按钮
	变量 账户管理14号按钮 为 按钮
	变量 账户管理15号按钮 为 按钮
	变量 账户管理16号按钮 为 按钮
	变量 账户管理17号按钮 为 按钮
	变量 账户管理18号按钮 为 按钮
	变量 账户管理19号按钮 为 按钮
	变量 账户管理20号按钮 为 按钮
	变量 账户管理21号按钮 为 按钮
	变量 账户管理22号按钮 为 按钮
	变量 账户管理23号按钮 为 按钮
	变量 账户管理24号按钮 为 按钮
	变量 账户管理25号按钮 为 按钮
	变量 账户管理26号按钮 为 按钮
	变量 账户管理27号按钮 为 按钮
	变量 账户管理28号按钮 为 按钮
	变量 账户管理29号按钮 为 按钮
	变量 账户管理30号按钮 为 按钮
	变量 账户管理31号按钮 为 按钮
	
	变量 账户射击记录高级列表框 为 高级列表框
	
	变量 账户射击记录统计折线图 为 折线图
	
	变量 账户管理查阅年份 为 整数型
	
	
过程 创建账户射击管理页面过程()
	
	账户管理查阅年份 = 2023
	
	'背景图
	账户管理背景 = 创建 图片框 位于 面板2
	账户管理背景.左边 = 公用模块.取宽度绝对像素(0)
	账户管理背景.顶边 = 公用模块.取高度绝对像素(16)
	账户管理背景.宽度 = 公用模块.取宽度绝对像素(800)
	账户管理背景.高度 = 公用模块.取高度绝对像素(452)
	账户管理背景.图像2 = R.big_bg
	
	账户管理人员管理高级列表框 = 创建 高级列表框 位于 面板2
	账户管理人员管理高级列表框.左边 = 公用模块.取宽度绝对像素(39)
	账户管理人员管理高级列表框.顶边 = 公用模块.取高度绝对像素(40)
	账户管理人员管理高级列表框.宽度 = 公用模块.取宽度绝对像素(215)
	账户管理人员管理高级列表框.高度 = 公用模块.取高度绝对像素(364)
	账户管理人员管理高级列表框.背景颜色 = 黑色
	账户管理人员管理高级列表框.标题字体颜色 = 白色
	账户管理人员管理高级列表框.信息字体颜色 = 灰色
	账户管理人员管理高级列表框.按钮字体颜色 = 白色
	账户管理人员管理高级列表框.插入项目(0,"","标题","项目信息","","编辑")
	账户管理人员管理高级列表框.插入项目(1,"","标题","项目信息","","编辑")
	账户管理人员管理高级列表框.插入项目(2,"","标题","项目信息","","编辑")
	账户管理人员管理高级列表框.插入项目(3,"","标题","项目信息","","编辑")
	账户管理人员管理高级列表框.插入项目(4,"","标题","项目信息","","编辑")
	账户管理人员管理高级列表框.插入项目(5,"","标题","项目信息","","编辑")
	
	账户管理添加账户按钮 = 创建 按钮 位于 面板2
	账户管理添加账户按钮.左边 = 公用模块.取宽度绝对像素(93)
	账户管理添加账户按钮.顶边 = 公用模块.取高度绝对像素(410)
	账户管理添加账户按钮.宽度 = 公用模块.取宽度绝对像素(87)
	账户管理添加账户按钮.高度 = 公用模块.取高度绝对像素(30)
	账户管理添加账户按钮.可停留焦点 = 假
	账户管理添加账户按钮.标题 = "增加账户"
	账户管理添加账户按钮.字体大小 = 公用模块.取字体绝对大小(12)
	账户管理添加账户按钮.字体颜色 = 白色
	账户管理添加账户按钮.字体颜色 = 白色
	账户管理添加账户按钮.对齐方式 = 靠中居中
	
	账户管理年份后退按钮 = 创建 按钮 位于 面板2
	账户管理年份后退按钮.左边 = 公用模块.取宽度绝对像素(390)
	账户管理年份后退按钮.顶边 = 公用模块.取高度绝对像素(50)
	账户管理年份后退按钮.宽度 = 公用模块.取宽度绝对像素(48)
	账户管理年份后退按钮.高度 = 公用模块.取高度绝对像素(30)
	账户管理年份后退按钮.可停留焦点 = 假
	账户管理年份后退按钮.标题 = "<<"
	账户管理年份后退按钮.字体大小 = 公用模块.取字体绝对大小(11)
	账户管理年份后退按钮.字体颜色 = 白色
	账户管理年份后退按钮.对齐方式 = 靠中居中
	
	账户管理年份显示标签 = 创建 标签 位于 面板2
	账户管理年份显示标签.左边 = 公用模块.取宽度绝对像素(501)
	账户管理年份显示标签.顶边 = 公用模块.取高度绝对像素(52)
	账户管理年份显示标签.宽度 = 公用模块.取宽度绝对像素(45)
	账户管理年份显示标签.高度 = 公用模块.取高度绝对像素(30)
	账户管理年份显示标签.标题 = 账户管理查阅年份 & "年"
	账户管理年份显示标签.字体颜色 = 白色
	
	账户管理年费前进按钮 = 创建 按钮 位于 面板2
	账户管理年费前进按钮.左边 = 公用模块.取宽度绝对像素(602)
	账户管理年费前进按钮.顶边 = 公用模块.取高度绝对像素(50)
	账户管理年费前进按钮.宽度 = 公用模块.取宽度绝对像素(48)
	账户管理年费前进按钮.高度 = 公用模块.取高度绝对像素(30)
	账户管理年费前进按钮.可停留焦点 = 假
	账户管理年费前进按钮.标题 = ">>"
	账户管理年费前进按钮.字体大小 = 公用模块.取字体绝对大小(11)
	账户管理年费前进按钮.字体颜色 = 白色
	账户管理年费前进按钮.对齐方式 = 靠中居中
	
	账户管理01月按钮 = 创建 按钮 位于 面板2
	账户管理01月按钮.左边 = 公用模块.取宽度绝对像素(298)
	账户管理01月按钮.顶边 = 公用模块.取高度绝对像素(78)
	账户管理01月按钮.宽度 = 公用模块.取宽度绝对像素(30)
	账户管理01月按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理01月按钮.可停留焦点 = 假
	账户管理01月按钮.标题 = "一月"
	账户管理01月按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理01月按钮.字体颜色 = 白色
	账户管理01月按钮.对齐方式 = 靠中居中
	
	账户管理02月按钮 = 创建 按钮 位于 面板2
	账户管理02月按钮.左边 = 公用模块.取宽度绝对像素(334)
	账户管理02月按钮.顶边 = 公用模块.取高度绝对像素(78)
	账户管理02月按钮.宽度 = 公用模块.取宽度绝对像素(30)
	账户管理02月按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理02月按钮.可停留焦点 = 假
	账户管理02月按钮.标题 = "二月"
	账户管理02月按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理02月按钮.字体颜色 = 白色
	账户管理02月按钮.对齐方式 = 靠中居中
	
	账户管理03月按钮 = 创建 按钮 位于 面板2
	账户管理03月按钮.左边 = 公用模块.取宽度绝对像素(370)
	账户管理03月按钮.顶边 = 公用模块.取高度绝对像素(78)
	账户管理03月按钮.宽度 = 公用模块.取宽度绝对像素(30)
	账户管理03月按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理03月按钮.可停留焦点 = 假
	账户管理03月按钮.标题 = "三月"
	账户管理03月按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理03月按钮.字体颜色 = 白色
	账户管理03月按钮.对齐方式 = 靠中居中
	
	账户管理04月按钮 = 创建 按钮 位于 面板2
	账户管理04月按钮.左边 = 公用模块.取宽度绝对像素(407)
	账户管理04月按钮.顶边 = 公用模块.取高度绝对像素(78)
	账户管理04月按钮.宽度 = 公用模块.取宽度绝对像素(30)
	账户管理04月按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理04月按钮.可停留焦点 = 假
	账户管理04月按钮.标题 = "四月"
	账户管理04月按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理04月按钮.字体颜色 = 白色
	账户管理04月按钮.对齐方式 = 靠中居中
	
	账户管理05月按钮 = 创建 按钮 位于 面板2
	账户管理05月按钮.左边 = 公用模块.取宽度绝对像素(443)
	账户管理05月按钮.顶边 = 公用模块.取高度绝对像素(78)
	账户管理05月按钮.宽度 = 公用模块.取宽度绝对像素(30)
	账户管理05月按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理05月按钮.可停留焦点 = 假
	账户管理05月按钮.标题 = "五月"
	账户管理05月按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理05月按钮.字体颜色 = 白色
	账户管理05月按钮.对齐方式 = 靠中居中
	
	账户管理06月按钮 = 创建 按钮 位于 面板2
	账户管理06月按钮.左边 = 公用模块.取宽度绝对像素(479)
	账户管理06月按钮.顶边 = 公用模块.取高度绝对像素(78)
	账户管理06月按钮.宽度 = 公用模块.取宽度绝对像素(30)
	账户管理06月按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理06月按钮.可停留焦点 = 假
	账户管理06月按钮.标题 = "六月"
	账户管理06月按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理06月按钮.字体颜色 = 白色
	账户管理06月按钮.对齐方式 = 靠中居中
	
	账户管理07月按钮 = 创建 按钮 位于 面板2
	账户管理07月按钮.左边 = 公用模块.取宽度绝对像素(516)
	账户管理07月按钮.顶边 = 公用模块.取高度绝对像素(78)
	账户管理07月按钮.宽度 = 公用模块.取宽度绝对像素(30)
	账户管理07月按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理07月按钮.可停留焦点 = 假
	账户管理07月按钮.标题 = "七月"
	账户管理07月按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理07月按钮.字体颜色 = 白色
	账户管理07月按钮.对齐方式 = 靠中居中
	
	账户管理08月按钮 = 创建 按钮 位于 面板2
	账户管理08月按钮.左边 = 公用模块.取宽度绝对像素(552)
	账户管理08月按钮.顶边 = 公用模块.取高度绝对像素(78)
	账户管理08月按钮.宽度 = 公用模块.取宽度绝对像素(30)
	账户管理08月按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理08月按钮.可停留焦点 = 假
	账户管理08月按钮.标题 = "八月"
	账户管理08月按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理08月按钮.字体颜色 = 白色
	账户管理08月按钮.对齐方式 = 靠中居中
	
	账户管理09月按钮 = 创建 按钮 位于 面板2
	账户管理09月按钮.左边 = 公用模块.取宽度绝对像素(589)
	账户管理09月按钮.顶边 = 公用模块.取高度绝对像素(78)
	账户管理09月按钮.宽度 = 公用模块.取宽度绝对像素(30)
	账户管理09月按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理09月按钮.可停留焦点 = 假
	账户管理09月按钮.标题 = "九月"
	账户管理09月按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理09月按钮.字体颜色 = 白色
	账户管理09月按钮.对齐方式 = 靠中居中
	
	账户管理10月按钮 = 创建 按钮 位于 面板2
	账户管理10月按钮.左边 = 公用模块.取宽度绝对像素(626)
	账户管理10月按钮.顶边 = 公用模块.取高度绝对像素(78)
	账户管理10月按钮.宽度 = 公用模块.取宽度绝对像素(30)
	账户管理10月按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理10月按钮.可停留焦点 = 假
	账户管理10月按钮.标题 = "十月"
	账户管理10月按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理10月按钮.字体颜色 = 白色
	账户管理10月按钮.对齐方式 = 靠中居中
	
	账户管理11月按钮 = 创建 按钮 位于 面板2
	账户管理11月按钮.左边 = 公用模块.取宽度绝对像素(662)
	账户管理11月按钮.顶边 = 公用模块.取高度绝对像素(78)
	账户管理11月按钮.宽度 = 公用模块.取宽度绝对像素(40)
	账户管理11月按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理11月按钮.可停留焦点 = 假
	账户管理11月按钮.标题 = "十一月"
	账户管理11月按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理11月按钮.字体颜色 = 白色
	账户管理11月按钮.对齐方式 = 靠中居中
	
	账户管理12月按钮 = 创建 按钮 位于 面板2
	账户管理12月按钮.左边 = 公用模块.取宽度绝对像素(711)
	账户管理12月按钮.顶边 = 公用模块.取高度绝对像素(78)
	账户管理12月按钮.宽度 = 公用模块.取宽度绝对像素(40)
	账户管理12月按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理12月按钮.可停留焦点 = 假
	账户管理12月按钮.标题 = "十二月"
	账户管理12月按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理12月按钮.字体颜色 = 白色
	账户管理12月按钮.对齐方式 = 靠中居中
	
	账户管理01号按钮 = 创建 按钮 位于 面板2
	账户管理01号按钮.左边 = 公用模块.取宽度绝对像素(310)
	账户管理01号按钮.顶边 = 公用模块.取高度绝对像素(102)
	账户管理01号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理01号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理01号按钮.可停留焦点 = 假
	账户管理01号按钮.标题 = "01"
	账户管理01号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理01号按钮.字体颜色 = 白色
	账户管理01号按钮.对齐方式 = 靠中居中
	
	账户管理02号按钮 = 创建 按钮 位于 面板2
	账户管理02号按钮.左边 = 公用模块.取宽度绝对像素(351)
	账户管理02号按钮.顶边 = 公用模块.取高度绝对像素(102)
	账户管理02号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理02号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理02号按钮.可停留焦点 = 假
	账户管理02号按钮.标题 = "02"
	账户管理02号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理02号按钮.字体颜色 = 白色
	账户管理02号按钮.对齐方式 = 靠中居中
	
	账户管理03号按钮 = 创建 按钮 位于 面板2
	账户管理03号按钮.左边 = 公用模块.取宽度绝对像素(393)
	账户管理03号按钮.顶边 = 公用模块.取高度绝对像素(102)
	账户管理03号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理03号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理03号按钮.可停留焦点 = 假
	账户管理03号按钮.标题 = "03"
	账户管理03号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理03号按钮.字体颜色 = 白色
	账户管理03号按钮.对齐方式 = 靠中居中
	
	账户管理04号按钮 = 创建 按钮 位于 面板2
	账户管理04号按钮.左边 = 公用模块.取宽度绝对像素(435)
	账户管理04号按钮.顶边 = 公用模块.取高度绝对像素(102)
	账户管理04号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理04号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理04号按钮.可停留焦点 = 假
	账户管理04号按钮.标题 = "04"
	账户管理04号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理04号按钮.字体颜色 = 白色
	账户管理04号按钮.对齐方式 = 靠中居中
	
	账户管理05号按钮 = 创建 按钮 位于 面板2
	账户管理05号按钮.左边 = 公用模块.取宽度绝对像素(477)
	账户管理05号按钮.顶边 = 公用模块.取高度绝对像素(102)
	账户管理05号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理05号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理05号按钮.可停留焦点 = 假
	账户管理05号按钮.标题 = "05"
	账户管理05号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理05号按钮.字体颜色 = 白色
	账户管理05号按钮.对齐方式 = 靠中居中
	
	账户管理06号按钮 = 创建 按钮 位于 面板2
	账户管理06号按钮.左边 = 公用模块.取宽度绝对像素(519)
	账户管理06号按钮.顶边 = 公用模块.取高度绝对像素(102)
	账户管理06号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理06号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理06号按钮.可停留焦点 = 假
	账户管理06号按钮.标题 = "06"
	账户管理06号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理06号按钮.字体颜色 = 白色
	账户管理06号按钮.对齐方式 = 靠中居中
	
	账户管理07号按钮 = 创建 按钮 位于 面板2
	账户管理07号按钮.左边 = 公用模块.取宽度绝对像素(561)
	账户管理07号按钮.顶边 = 公用模块.取高度绝对像素(102)
	账户管理07号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理07号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理07号按钮.可停留焦点 = 假
	账户管理07号按钮.标题 = "07"
	账户管理07号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理07号按钮.字体颜色 = 白色
	账户管理07号按钮.对齐方式 = 靠中居中

	账户管理08号按钮 = 创建 按钮 位于 面板2
	账户管理08号按钮.左边 = 公用模块.取宽度绝对像素(603)
	账户管理08号按钮.顶边 = 公用模块.取高度绝对像素(102)
	账户管理08号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理08号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理08号按钮.可停留焦点 = 假
	账户管理08号按钮.标题 = "08"
	账户管理08号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理08号按钮.字体颜色 = 白色
	账户管理08号按钮.对齐方式 = 靠中居中
	
	账户管理09号按钮 = 创建 按钮 位于 面板2
	账户管理09号按钮.左边 = 公用模块.取宽度绝对像素(644)
	账户管理09号按钮.顶边 = 公用模块.取高度绝对像素(102)
	账户管理09号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理09号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理09号按钮.可停留焦点 = 假
	账户管理09号按钮.标题 = "09"
	账户管理09号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理09号按钮.字体颜色 = 白色
	账户管理09号按钮.对齐方式 = 靠中居中

	账户管理10号按钮 = 创建 按钮 位于 面板2
	账户管理10号按钮.左边 = 公用模块.取宽度绝对像素(686)
	账户管理10号按钮.顶边 = 公用模块.取高度绝对像素(102)
	账户管理10号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理10号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理10号按钮.可停留焦点 = 假
	账户管理10号按钮.标题 = "10"
	账户管理10号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理10号按钮.字体颜色 = 白色
	账户管理10号按钮.对齐方式 = 靠中居中
	
	账户管理11号按钮 = 创建 按钮 位于 面板2
	账户管理11号按钮.左边 = 公用模块.取宽度绝对像素(728)
	账户管理11号按钮.顶边 = 公用模块.取高度绝对像素(102)
	账户管理11号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理11号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理11号按钮.可停留焦点 = 假
	账户管理11号按钮.标题 = "11"
	账户管理11号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理11号按钮.字体颜色 = 白色
	账户管理11号按钮.对齐方式 = 靠中居中
	
	账户管理12号按钮 = 创建 按钮 位于 面板2
	账户管理12号按钮.左边 = 公用模块.取宽度绝对像素(310)
	账户管理12号按钮.顶边 = 公用模块.取高度绝对像素(125)
	账户管理12号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理12号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理12号按钮.可停留焦点 = 假
	账户管理12号按钮.标题 = "12"
	账户管理12号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理12号按钮.字体颜色 = 白色
	账户管理12号按钮.对齐方式 = 靠中居中
	
	账户管理13号按钮 = 创建 按钮 位于 面板2
	账户管理13号按钮.左边 = 公用模块.取宽度绝对像素(351)
	账户管理13号按钮.顶边 = 公用模块.取高度绝对像素(125)
	账户管理13号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理13号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理13号按钮.可停留焦点 = 假
	账户管理13号按钮.标题 = "13"
	账户管理13号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理13号按钮.字体颜色 = 白色
	账户管理13号按钮.对齐方式 = 靠中居中
	
	账户管理14号按钮 = 创建 按钮 位于 面板2
	账户管理14号按钮.左边 = 公用模块.取宽度绝对像素(393)
	账户管理14号按钮.顶边 = 公用模块.取高度绝对像素(125)
	账户管理14号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理14号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理14号按钮.可停留焦点 = 假
	账户管理14号按钮.标题 = "14"
	账户管理14号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理14号按钮.字体颜色 = 白色
	账户管理14号按钮.对齐方式 = 靠中居中
	
	账户管理15号按钮 = 创建 按钮 位于 面板2
	账户管理15号按钮.左边 = 公用模块.取宽度绝对像素(435)
	账户管理15号按钮.顶边 = 公用模块.取高度绝对像素(125)
	账户管理15号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理15号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理15号按钮.可停留焦点 = 假
	账户管理15号按钮.标题 = "15"
	账户管理15号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理15号按钮.字体颜色 = 白色
	账户管理15号按钮.对齐方式 = 靠中居中
	
	账户管理16号按钮 = 创建 按钮 位于 面板2
	账户管理16号按钮.左边 = 公用模块.取宽度绝对像素(477)
	账户管理16号按钮.顶边 = 公用模块.取高度绝对像素(125)
	账户管理16号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理16号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理16号按钮.可停留焦点 = 假
	账户管理16号按钮.标题 = "16"
	账户管理16号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理16号按钮.字体颜色 = 白色
	账户管理16号按钮.对齐方式 = 靠中居中
	
	账户管理17号按钮 = 创建 按钮 位于 面板2
	账户管理17号按钮.左边 = 公用模块.取宽度绝对像素(519)
	账户管理17号按钮.顶边 = 公用模块.取高度绝对像素(125)
	账户管理17号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理17号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理17号按钮.可停留焦点 = 假
	账户管理17号按钮.标题 = "17"
	账户管理17号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理17号按钮.字体颜色 = 白色
	账户管理17号按钮.对齐方式 = 靠中居中
	
	账户管理18号按钮 = 创建 按钮 位于 面板2
	账户管理18号按钮.左边 = 公用模块.取宽度绝对像素(561)
	账户管理18号按钮.顶边 = 公用模块.取高度绝对像素(125)
	账户管理18号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理18号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理18号按钮.可停留焦点 = 假
	账户管理18号按钮.标题 = "18"
	账户管理18号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理18号按钮.字体颜色 = 白色
	账户管理18号按钮.对齐方式 = 靠中居中
	
	账户管理19号按钮 = 创建 按钮 位于 面板2
	账户管理19号按钮.左边 = 公用模块.取宽度绝对像素(603)
	账户管理19号按钮.顶边 = 公用模块.取高度绝对像素(125)
	账户管理19号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理19号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理19号按钮.可停留焦点 = 假
	账户管理19号按钮.标题 = "19"
	账户管理19号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理19号按钮.字体颜色 = 白色
	账户管理19号按钮.对齐方式 = 靠中居中
	
	账户管理20号按钮 = 创建 按钮 位于 面板2
	账户管理20号按钮.左边 = 公用模块.取宽度绝对像素(644)
	账户管理20号按钮.顶边 = 公用模块.取高度绝对像素(125)
	账户管理20号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理20号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理20号按钮.可停留焦点 = 假
	账户管理20号按钮.标题 = "20"
	账户管理20号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理20号按钮.字体颜色 = 白色
	账户管理20号按钮.对齐方式 = 靠中居中

	账户管理21号按钮 = 创建 按钮 位于 面板2
	账户管理21号按钮.左边 = 公用模块.取宽度绝对像素(686)
	账户管理21号按钮.顶边 = 公用模块.取高度绝对像素(125)
	账户管理21号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理21号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理21号按钮.可停留焦点 = 假
	账户管理21号按钮.标题 = "21"
	账户管理21号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理21号按钮.字体颜色 = 白色
	账户管理21号按钮.对齐方式 = 靠中居中
	
	账户管理22号按钮 = 创建 按钮 位于 面板2
	账户管理22号按钮.左边 = 公用模块.取宽度绝对像素(728)
	账户管理22号按钮.顶边 = 公用模块.取高度绝对像素(125)
	账户管理22号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理22号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理22号按钮.可停留焦点 = 假
	账户管理22号按钮.标题 = "22"
	账户管理22号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理22号按钮.字体颜色 = 白色
	账户管理22号按钮.对齐方式 = 靠中居中

	账户管理23号按钮 = 创建 按钮 位于 面板2
	账户管理23号按钮.左边 = 公用模块.取宽度绝对像素(310)
	账户管理23号按钮.顶边 = 公用模块.取高度绝对像素(149)
	账户管理23号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理23号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理23号按钮.可停留焦点 = 假
	账户管理23号按钮.标题 = "23"
	账户管理23号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理23号按钮.字体颜色 = 白色
	账户管理23号按钮.对齐方式 = 靠中居中
	
	账户管理24号按钮 = 创建 按钮 位于 面板2
	账户管理24号按钮.左边 = 公用模块.取宽度绝对像素(351)
	账户管理24号按钮.顶边 = 公用模块.取高度绝对像素(149)
	账户管理24号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理24号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理24号按钮.可停留焦点 = 假
	账户管理24号按钮.标题 = "24"
	账户管理24号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理24号按钮.字体颜色 = 白色
	账户管理24号按钮.对齐方式 = 靠中居中
	
	账户管理25号按钮 = 创建 按钮 位于 面板2
	账户管理25号按钮.左边 = 公用模块.取宽度绝对像素(393)
	账户管理25号按钮.顶边 = 公用模块.取高度绝对像素(149)
	账户管理25号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理25号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理25号按钮.可停留焦点 = 假
	账户管理25号按钮.标题 = "25"
	账户管理25号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理25号按钮.字体颜色 = 白色
	账户管理25号按钮.对齐方式 = 靠中居中

	账户管理26号按钮 = 创建 按钮 位于 面板2
	账户管理26号按钮.左边 = 公用模块.取宽度绝对像素(435)
	账户管理26号按钮.顶边 = 公用模块.取高度绝对像素(149)
	账户管理26号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理26号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理26号按钮.可停留焦点 = 假
	账户管理26号按钮.标题 = "26"
	账户管理26号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理26号按钮.字体颜色 = 白色
	账户管理26号按钮.对齐方式 = 靠中居中
	
	账户管理27号按钮 = 创建 按钮 位于 面板2
	账户管理27号按钮.左边 = 公用模块.取宽度绝对像素(477)
	账户管理27号按钮.顶边 = 公用模块.取高度绝对像素(149)
	账户管理27号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理27号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理27号按钮.可停留焦点 = 假
	账户管理27号按钮.标题 = "27"
	账户管理27号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理27号按钮.字体颜色 = 白色
	账户管理27号按钮.对齐方式 = 靠中居中
	
	账户管理28号按钮 = 创建 按钮 位于 面板2
	账户管理28号按钮.左边 = 公用模块.取宽度绝对像素(519)
	账户管理28号按钮.顶边 = 公用模块.取高度绝对像素(149)
	账户管理28号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理28号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理28号按钮.可停留焦点 = 假
	账户管理28号按钮.标题 = "28"
	账户管理28号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理28号按钮.字体颜色 = 白色
	账户管理28号按钮.对齐方式 = 靠中居中

	账户管理29号按钮 = 创建 按钮 位于 面板2
	账户管理29号按钮.左边 = 公用模块.取宽度绝对像素(561)
	账户管理29号按钮.顶边 = 公用模块.取高度绝对像素(149)
	账户管理29号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理29号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理29号按钮.可停留焦点 = 假
	账户管理29号按钮.标题 = "29"
	账户管理29号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理29号按钮.字体颜色 = 白色
	账户管理29号按钮.对齐方式 = 靠中居中

	账户管理30号按钮 = 创建 按钮 位于 面板2
	账户管理30号按钮.左边 = 公用模块.取宽度绝对像素(603)
	账户管理30号按钮.顶边 = 公用模块.取高度绝对像素(149)
	账户管理30号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理30号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理30号按钮.可停留焦点 = 假
	账户管理30号按钮.标题 = "30"
	账户管理30号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理30号按钮.字体颜色 = 白色
	账户管理30号按钮.对齐方式 = 靠中居中
	
	账户管理31号按钮 = 创建 按钮 位于 面板2
	账户管理31号按钮.左边 = 公用模块.取宽度绝对像素(644)
	账户管理31号按钮.顶边 = 公用模块.取高度绝对像素(149)
	账户管理31号按钮.宽度 = 公用模块.取宽度绝对像素(25)
	账户管理31号按钮.高度 = 公用模块.取高度绝对像素(25)
	账户管理31号按钮.可停留焦点 = 假
	账户管理31号按钮.标题 = "31"
	账户管理31号按钮.字体大小 = 公用模块.取字体绝对大小(6)
	账户管理31号按钮.字体颜色 = 白色
	账户管理31号按钮.对齐方式 = 靠中居中
	
	账户射击记录高级列表框 = 创建 高级列表框 位于 面板2
	账户射击记录高级列表框.左边 = 公用模块.取宽度绝对像素(279)
	账户射击记录高级列表框.顶边 = 公用模块.取高度绝对像素(195)
	账户射击记录高级列表框.宽度 = 公用模块.取宽度绝对像素(127)
	账户射击记录高级列表框.高度 = 公用模块.取高度绝对像素(198)
	账户射击记录高级列表框.背景颜色 = 黑色
	账户射击记录高级列表框.标题字体颜色 = 白色
	账户射击记录高级列表框.信息字体颜色 = 灰色
	账户射击记录高级列表框.按钮字体颜色 = 白色
	账户射击记录高级列表框.插入项目(0,"","16:25:66","","","")
	账户射击记录高级列表框.插入项目(1,"","16:25:66","","","")
	账户射击记录高级列表框.插入项目(2,"","16:25:66","","","")
	账户射击记录高级列表框.插入项目(3,"","16:25:66","","","")
	账户射击记录高级列表框.插入项目(4,"","16:25:66","","","")
	账户射击记录高级列表框.插入项目(5,"","16:25:66","","","")
	账户射击记录高级列表框.插入项目(6,"","16:25:66","","","")
	账户射击记录高级列表框.插入项目(7,"","16:25:66","","","")
	账户射击记录高级列表框.插入项目(8,"","16:25:66","","","")
	账户射击记录高级列表框.插入项目(9,"","16:25:66","","","")
	账户射击记录高级列表框.插入项目(10,"","16:25:66","","","")
	
	'弹出面板
	面板2.开启特效(5,100,真)
	高级对话框1.弹出对话框()
结束 过程


事件 账户管理年份后退按钮.被单击()
	账户管理查阅年份 = 账户管理查阅年份 - 1
	账户管理年份显示标签.标题 = 账户管理查阅年份 & "年"
结束 事件

事件 账户管理年费前进按钮.被单击()
	账户管理查阅年份 = 账户管理查阅年份 + 1
	账户管理年份显示标签.标题 = 账户管理查阅年份 & "年"
结束 事件


