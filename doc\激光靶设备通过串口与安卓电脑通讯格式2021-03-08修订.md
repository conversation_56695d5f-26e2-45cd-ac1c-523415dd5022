波特率115200，校验位:NONE，8位数据，1位停止位。
2021.2.3新制订数据格式：
JSON字符串(约束长度<=30位):
射击结果格式:
{
"x":"-50~50",坐标X
"y":"-40~60",坐标Y
"h":"0~10.9"环值
}
单精度小数就是后一位，双精度小数就是后两位。
整数前不用补0，带小数点的保留小数点后2位。
射击结果终端串口发给安卓电脑
@shooting:{"x":-50.00,"y":-40.00,"h":10.90};@shooting:{\"result\":\"ok\"};


2021.2.28日增加格式
设备在线数据(0:为不在线;1:为在线)
客户端@state:{"gun":1,"target":1};->服务器@state:{\"result\":\"ok\"};
2021-03-08调整部分,终端在特定时间没有收到服务器回应,使用LED状态回馈

2021.3.7日增加格式
信道修改(服务器设置范围0~119),客户端回应结果(0:信道修改失败;1:信道修改成功)
服务器@channel:{\"channel\":119};客户端4秒内回应@channel:{"gun":1,"target":1};

拉栓上弹
客户端@clip:{"change":"ok"};服务器@clip:{\"result\":\"ok\"};

2021.3.8
上位机允许设置射击次数

bridgeRedState0.png
bridgeRedState1.png

gunRedState0.png
gunRedState1.png

targetRedState0.png
targetRedState1.png


    'WIFI管理1.置WIFI状态(假)
    'WIFI管理1.创建热点("HDDZ2G1","88888888")
    'WIFI管理1.置WIFI状态(真)
    'WIFI状态按钮.开启特效(5,100,假)
    '高级对话框背景 = 创建 图片框 位于 面板2
    '高级对话框背景.图像 = "Tips1.png"
    '高级对话框背景.左边 = 取相对像素(82)
    '高级对话框背景.顶边 = 取相对像素(50)
    '高级对话框背景.宽度 = 取相对像素(160)
    '高级对话框背景.高度 = 取相对像素(100)
    
    '高级对话框标题 = 创建 标签 位于 面板2
    '高级对话框标题.标题 = "测试的标题"
    '高级对话框标题.左边 = 取相对像素(10)
    '高级对话框标题.顶边 = 取相对像素(5)
    '高级对话框标题.宽度 = 取相对像素(100)
    '高级对话框标题.高度 = 取相对像素(24)
    '高级对话框标题.字体颜色 = 白色
    '高级对话框标题.字体大小 = 12
    '高级对话框标题.对齐方式 = 靠中居中
    '面板2.开启特效(5,300,真)
    '高级对话框1.弹出对话框()


    基站工作状态

    WIFI管理1.创建热点(热点信息,"88888888")

    变量 客户端001地址 为 文本型
    变量 客户端001端口 为 整数型

    WiFiHotspotStatus

    修改数据库("config", "WiFiHotspotStatus", "value",弹孔叠加结果)

如果 基站状态 = "开" 则

结束 如果
    