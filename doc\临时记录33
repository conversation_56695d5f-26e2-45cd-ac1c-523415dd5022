maximum_size=32768


10240


http://172.16.1.9/HDLauncher/APPShop.html


	变量 格式头 为 文本型(4)
	变量 APP标识符 为 文本型(1)
	变量 数据包ID 为 文本型(4)
	变量 协议号 为 文本型(1)
	变量 确认数据包ID 为 文本型(4)
	变量 上发数据协议号 为 文本型(1)
	变量 上发的设备ID 为 文本型(14)
	变量 校验位 为 文本型(4)
	变量 结束符号 为 文本型(2)
	变量 累加数值 为 整数型
	变量 网管数据包 为 文本型(35)
	
	格式头(0) = 文本分割(0)
	格式头(1) = 文本分割(1)
	格式头(2) = 文本分割(2)
	格式头(3) = 文本分割(3)
	
	APP标识符(0) = 文本分割(4)
	
	'累加开始
	数据包ID(0) = 文本分割(5)
	数据包ID(1) = 文本分割(6)
	数据包ID(2) = 文本分割(7)
	数据包ID(3) = 文本分割(8)
	
	协议号(0) = 文本分割(9)
	
	确认数据包ID(0) = 文本分割(5)
	确认数据包ID(1) = 文本分割(6)
	确认数据包ID(2) = 文本分割(7)
	确认数据包ID(3) = 文本分割(8)
	
	上发数据协议号(0) = 文本分割(9)
	
	上发的设备ID(0) = 文本分割(10)
	上发的设备ID(1) = 文本分割(11)
	上发的设备ID(2) = 文本分割(12)
	上发的设备ID(3) = 文本分割(13)
	上发的设备ID(4) = 文本分割(14)
	上发的设备ID(5) = 文本分割(15)
	上发的设备ID(6) = 文本分割(16)
	上发的设备ID(7) = 文本分割(17)
	上发的设备ID(8) = 文本分割(18)
	上发的设备ID(9) = 文本分割(19)
	上发的设备ID(10) = 文本分割(20)
	上发的设备ID(11) = 文本分割(21)
	上发的设备ID(12) = 文本分割(22)
	上发的设备ID(13) = 文本分割(23)
	'累加结束
	
	累加数值 = 数据包ID(0) + 数据包ID(1) + 数据包ID(2) + 数据包ID(3) + 协议号(0) + 确认数据包ID(0) + 确认数据包ID(1) + 确认数据包ID(2) + 确认数据包ID(3) + 上发数据协议号(0) + 上发的设备ID(1) + 上发的设备ID(2) + 上发的设备ID(3) + 上发的设备ID(4) + 上发的设备ID(5) + 上发的设备ID(6) + 上发的设备ID(7) + 上发的设备ID(8) + 上发的设备ID(9) + 上发的设备ID(10) + 上发的设备ID(11) + 上发的设备ID(12) + 上发的设备ID(13)
	弹出提示(累加数值)
	
	'校验位(0) = 
	'校验位(1) = 
	'校验位(2) = 
	'校验位(3) = 
	
	结束符号(0) = 文本分割(37)
	结束符号(1) = 文本分割(38)
	
    '服务器1.发送数据()	