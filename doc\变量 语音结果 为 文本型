
事件 语音单选框男.选择改变()
	如果 语音单选框男.选中 = 真 则
		语音单选框女.选中 = 假
		语音结果 = "男"
		修改数据库("config", "voice", "value",语音结果)
	结束 如果
结束 事件

事件 语音单选框女.选择改变()
	如果 语音单选框女.选中 = 真 则
		语音单选框男.选中 = 假
		语音结果 = "女"
		修改数据库("config", "voice", "value",语音结果)
	结束 如果
结束 事件

'弹孔叠加
事件 弹孔叠加单选框是.选择改变()
	如果 弹孔叠加单选框是.选中 = 真 则
		弹孔叠加单选框否.选中 = 假
		弹孔叠加结果 = "是"
		修改数据库("config", "bulletStack", "value",弹孔叠加结果)
	结束 如果
结束 事件

事件 弹孔叠加单选框否.选择改变()
	如果 弹孔叠加单选框否.选中 = 真 则
		弹孔叠加单选框是.选中 = 假
		弹孔叠加结果 = "否"
		修改数据库("config", "bulletStack", "value",弹孔叠加结果)
	结束 如果
结束 事件

'自动结束
事件 自动结束单选框是.选择改变()
	如果 自动结束单选框是.选中 = 真 则
		自动结束单选框否.选中 = 假
		自动结束结果 = "是"
		修改数据库("config", "autoend", "value",自动结束结果)
	结束 如果
结束 事件

事件 自动结束单选框否.选择改变()
	如果 自动结束单选框否.选中 = 真 则
		自动结束单选框是.选中 = 假
		自动结束结果 = "否"
		修改数据库("config", "autoend", "value",自动结束结果)
	结束 如果
结束 事件


	变量 频点设置背景 为 图片框
	变量 频点设置标题 为 标签
	变量 频点设置内容 为 标签
	变量 频点设置水平滑块条 为 水平滑块条
	变量 设置频点确定按钮 为 按钮
    变量 设置频点取消按钮 为 按钮
	变量 当前频点频率 为 文本型

	频点设置按钮.开启特效(5,100,假)
	频点设置背景 = 创建 图片框 位于 面板2
	频点设置背景.图像 = "Tips1.png"
	频点设置背景.左边 = 290
	频点设置背景.顶边 = 170
	频点设置背景.宽度 = 673
	频点设置背景.高度 = 436
	频点设置标题 = 创建 标签 位于 面板2
	频点设置标题.标题 = "配置超声波无线频点"
	频点设置标题.左边 = 502
	频点设置标题.顶边 = 190
	频点设置标题.宽度 = 270
	频点设置标题.高度 = 30
	频点设置标题.字体颜色 = 白色
	频点设置标题.字体大小 = 12
	频点设置标题.对齐方式 = 靠中居中
	
	频点设置内容 = 创建 标签 位于 面板2
	频点设置内容.标题 = "当前频点:" & 当前频点频率
	频点设置内容.左边 = 548
	频点设置内容.顶边 = 260
	频点设置内容.宽度 = 186
	频点设置内容.高度 = 34
	频点设置内容.字体颜色 = 白色
	频点设置内容.字体大小 = 12
	频点设置内容.对齐方式 = 靠中居中
	
	频点设置水平滑块条 = 创建 水平滑块条 位于 面板2
	频点设置水平滑块条.左边 = 335
	频点设置水平滑块条.顶边 = 328
	频点设置水平滑块条.宽度 = 588
	频点设置水平滑块条.高度 = 30
	频点设置水平滑块条.位置 = 到整数(当前频点频率)
	频点设置水平滑块条.最大位置 = 119
	
    设置频点确定按钮 = 创建 按钮 位于 面板2
    设置频点确定按钮.左边 = 545
    设置频点确定按钮.顶边 = 438
    设置频点确定按钮.宽度 = 192
    设置频点确定按钮.高度 = 49
    设置频点确定按钮.标题 = "确定"
    设置频点确定按钮.字体颜色 = 白色
	设置频点确定按钮.字体大小 = 12
	设置频点确定按钮.对齐方式 = 靠中居中

    设置频点取消按钮 = 创建 按钮 位于 面板2
    设置频点取消按钮.左边 = 545
    设置频点取消按钮.顶边 = 508
    设置频点取消按钮.宽度 = 192
    设置频点取消按钮.高度 = 49
    设置频点取消按钮.标题 = "取消"
    设置频点取消按钮.字体颜色 = 白色
	设置频点取消按钮.字体大小 = 12
	设置频点取消按钮.对齐方式 = 靠中居中
	
	面板2.开启特效(5,300,真)
	高级对话框1.弹出对话框()

	事件 WIFI热点设置确定按钮.被单击()


	修改数据库("config", "WiFiSequence", "value",基站频道值)