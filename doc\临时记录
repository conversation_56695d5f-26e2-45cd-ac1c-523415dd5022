UPDATE `config` SET value = '女' WHERE type= 'voice'


16进制C=12
16进制e=14

12+14 = 26

0xce = 206

1280x800坐标系范围:
Y:-410至290  Y坐标系数和:700  X转化公式:700 / 100 = 7
X:-360至360  X坐标系数和:720  Y转化公式:720 / 100 = 7.2
右X:290 / 60 = 4.83333333 左X:410 / 40 = 10.25
Y:360 / 50 = 7.2

2160x1080
Y:-540至380  Y坐标系数和:920  X转化公式:920 / 100 = 9.2
X:-480至480  X坐标系数和:960  Y转化公式:960 / 100 = 9.6
右X:380 / 60 = 6.33333333 左X:480 / 40 = 12
Y:480 / 50 = 9.6

单片机传输坐标系范围:
 Y:-40至60  Y坐标系数和:100
 X -50至50  X坐标系数和:100

++

40 40 50 65 A0 30 30 30 37 01 46 35 36 41 43 35 39 33 30 32 36 32 37 34 07 33 32 30 30 34 36 34 39 30 35 37 42 0A 0D

40 40 50 65 A0 30 30 30 37 01 46 35 36 41 43 35 39 33 30 32 36 32 37 34 07 43 45 30 30 34 36 34 39 30 35 37 42 0A 0D

428

-329

-301
switch(Value)
 {
  case 0:n=0x30;break;
  case 1:n=0x31;break;
  case 2:n=0x32;break;
  case 3:n=0x33;break;
  case 4:n=0x34;break;
  case 5:n=0x35;break;
  case 6:n=0x36;break;
  case 7:n=0x37;break;
  case 8:n=0x38;break;
  case 9:n=0x39;break;
  case 10:n=0x41;break;
  case 11:n=0x42;break;
  case 12:n=0x43;break;
  case 13:n=0x44;break;
  case 14:n=0x45;break;
  case 15:n=0x46;break;
 }
 return n;
}

414


up
dow
right
rightup
rightdow
left
leftup
leftdow

面板设置背景
面板设置描述
靶环设置值
靶环设置加
靶环设置减

中心点设置背景
中心点设置描述
中心点设置值
靶环中心点X减
靶环中心点X加
靶环中心点Y减
靶环中心点Y加

coreX
coreY

坐标系Y范围背景
Y范围设置描述
Y范围设置值
Y范围上减
Y范围上加
Y范围下减
Y范围下加


坐标系X范围背景
X范围设置描述
X范围设置值
X范围左减
X范围左加
X范围右减
X范围右加

upEdgeY
dowEdgeY
leftEdgeX
rightEdgeX

日志输出

422
109
178
229

弹孔偏移背景
弹孔偏移设置
弹孔偏移设置描述
弹孔偏移设置值
弹孔偏移X减
弹孔偏移X加
弹孔偏移Y减
弹孔偏移Y加

bulletOffsetX
bulletOffsetY
482
-324

信息标签

信息标签.标题
屏幕分辨率
com.mobile.ChestRingTargetTrain